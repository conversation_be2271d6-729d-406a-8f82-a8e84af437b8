# دليل البدء السريع - نظام إدارة المبيعات والمشتريات

## 🚀 التشغيل السريع

### 1. تحميل المتطلبات
- تحميل وتثبيت [.NET 6 SDK](https://dotnet.microsoft.com/download/dotnet/6.0)
- تحميل وتثبيت [SQL Server Express](https://www.microsoft.com/en-us/sql-server/sql-server-downloads) (مجاني)

### 2. إعداد قاعدة البيانات
```cmd
# فتح SQL Server Management Studio وتشغيل ملف
setup_database.sql

# أو استخدام Command Line
sqlcmd -S .\SQLEXPRESS -i setup_database.sql
```

### 3. تشغيل التطبيق
```cmd
# الطريقة السهلة
run.bat

# أو يدوياً
dotnet restore
dotnet build
dotnet run
```

### 4. تسجيل الدخول
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## 📋 الوظائف المتاحة حالياً

### ✅ مكتملة ومجربة
- [x] تسجيل الدخول والأمان
- [x] إدارة العملاء (إضافة، تعديل، حذف، بحث)
- [x] إدارة الموردين (إضافة، تعديل، حذف، بحث)
- [x] إدارة الأصناف (إضافة، تعديل، حذف، بحث)
- [x] إدارة فواتير البيع (إضافة، تعديل، حذف، ترحيل)
- [x] النموذج الرئيسي مع القوائم الشاملة

### 🔄 قيد التطوير
- [ ] تفاصيل فواتير البيع (إضافة الأصناف)
- [ ] إدارة المشتريات
- [ ] إدارة المخازن والمخزون
- [ ] الإدارة المالية والخزائن
- [ ] التقارير المالية

## 🏗️ هيكل المشروع

```
POSSystem/
├── Models/              # نماذج البيانات
├── Data/               # طبقة الوصول للبيانات
│   ├── Interfaces/     # واجهات Repository
│   └── Repositories/   # تطبيق Repository
├── Services/           # طبقة منطق الأعمال
├── Forms/              # نماذج واجهة المستخدم
├── Database/           # ملفات قاعدة البيانات
└── Utils/              # الأدوات المساعدة
```

## 🔧 إضافة وحدة جديدة

### 1. إنشاء النموذج (Model)
```csharp
public class NewEntity : BaseEntity
{
    [Required]
    public string Name { get; set; } = string.Empty;
    // خصائص أخرى...
}
```

### 2. إنشاء Repository Interface
```csharp
public interface INewEntityRepository : IRepository<NewEntity>
{
    Task<NewEntity?> GetByNameAsync(string name);
    // دوال إضافية...
}
```

### 3. تطبيق Repository
```csharp
public class NewEntityRepository : INewEntityRepository
{
    // تطبيق الدوال...
}
```

### 4. إنشاء Service Interface
```csharp
public interface INewEntityService
{
    Task<IEnumerable<NewEntity>> GetAllAsync();
    // دوال الخدمة...
}
```

### 5. تطبيق Service
```csharp
public class NewEntityService : INewEntityService
{
    // تطبيق منطق الأعمال...
}
```

### 6. إنشاء النموذج (Form)
```csharp
public partial class NewEntityForm : Form
{
    private readonly INewEntityService _service;
    // تطبيق واجهة المستخدم...
}
```

### 7. تسجيل في Program.cs
```csharp
services.AddScoped<INewEntityRepository, NewEntityRepository>();
services.AddScoped<INewEntityService, NewEntityService>();
services.AddTransient<NewEntityForm>();
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في الاتصال بقاعدة البيانات
```
Error: Cannot connect to database
```
**الحل**: تحقق من:
- تشغيل SQL Server
- صحة connection string في appsettings.json
- وجود قاعدة البيانات POSSystem

#### 2. خطأ في تشغيل التطبيق
```
Error: .NET SDK not found
```
**الحل**: تثبيت .NET 6 SDK من الموقع الرسمي

#### 3. خطأ في البناء
```
Build failed
```
**الحل**: 
```cmd
dotnet clean
dotnet restore
dotnet build
```

## 📝 ملاحظات للمطورين

### أفضل الممارسات
1. **استخدام Repository Pattern** لفصل طبقة البيانات
2. **Dependency Injection** لإدارة التبعيات
3. **Logging** لتسجيل الأحداث والأخطاء
4. **Validation** للتحقق من صحة البيانات
5. **Exception Handling** لمعالجة الأخطاء

### قواعد التسمية
- **Models**: PascalCase (Customer, SalesInvoice)
- **Properties**: PascalCase (CustomerName, TotalAmount)
- **Methods**: PascalCase (GetCustomerAsync, AddInvoiceAsync)
- **Variables**: camelCase (customerId, invoiceTotal)
- **Database**: PascalCase للجداول والأعمدة

### إرشادات الكود
- استخدام `async/await` للعمليات غير المتزامنة
- معالجة الأخطاء في جميع الطبقات
- التحقق من صحة البيانات قبل الحفظ
- استخدام `using` statements للموارد
- توثيق الكود بالتعليقات العربية

## 🔮 الخطوات التالية

### المرحلة القادمة
1. إكمال تفاصيل فواتير البيع
2. إضافة وحدة المشتريات
3. تطوير إدارة المخزون
4. إضافة التقارير الأساسية
5. تحسين واجهة المستخدم

### ميزات مستقبلية
- تطبيق الهاتف المحمول
- واجهة ويب
- تكامل مع أنظمة الدفع
- تقارير متقدمة مع الرسوم البيانية
- نظام الإشعارات

## 📞 الدعم والمساعدة

للحصول على المساعدة:
1. مراجعة هذا الدليل
2. فحص ملفات Log للأخطاء
3. التحقق من GitHub Issues
4. التواصل مع فريق التطوير

---

**ملاحظة**: هذا النظام في مرحلة التطوير النشط. يرجى التحقق من التحديثات بانتظام.
