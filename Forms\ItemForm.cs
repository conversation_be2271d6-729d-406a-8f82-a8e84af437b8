using Microsoft.Extensions.Logging;
using POSSystem.Models;
using POSSystem.Services;
using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace POSSystem.Forms
{
    public partial class ItemForm : Form
    {
        private readonly IItemService _itemService;
        private readonly ILogger<ItemForm> _logger;

        // Controls
        private Panel topPanel;
        private Panel bottomPanel;
        private Panel centerPanel;
        private DataGridView itemsGrid;
        private Button addButton;
        private Button editButton;
        private Button deleteButton;
        private Button refreshButton;
        private TextBox searchTextBox;
        private Label searchLabel;

        // Item details panel
        private GroupBox itemDetailsGroup;
        private TextBox itemCodeTextBox;
        private TextBox itemNameTextBox;
        private TextBox itemNameEnTextBox;
        private ComboBox categoryComboBox;
        private ComboBox baseUnitComboBox;
        private TextBox barcodeTextBox;
        private Button generateBarcodeButton;
        private TextBox descriptionTextBox;
        private NumericUpDown minStockLevelNumeric;
        private NumericUpDown maxStockLevelNumeric;
        private NumericUpDown reorderLevelNumeric;
        private CheckBox hasExpiryCheckBox;
        private CheckBox isSerialCheckBox;
        private CheckBox isActiveCheckBox;
        private Button saveButton;
        private Button cancelButton;

        private Item? _currentItem;
        private bool _isEditing = false;

        public ItemForm(IItemService itemService, ILogger<ItemForm> logger)
        {
            _itemService = itemService;
            _logger = logger;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "إدارة الأصناف";
            this.Size = new Size(1400, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateTopPanel();
            CreateBottomPanel();
            CreateCenterPanel();
            CreateItemDetailsPanel();

            this.Load += ItemForm_Load;

            this.ResumeLayout(false);
        }

        private void CreateTopPanel()
        {
            topPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(248, 249, 250)
            };

            // Search controls
            searchLabel = new Label
            {
                Text = "البحث:",
                Location = new Point(1250, 20),
                Size = new Size(50, 20),
                Font = new Font("Tahoma", 10)
            };

            searchTextBox = new TextBox
            {
                Location = new Point(1050, 18),
                Size = new Size(200, 25),
                Font = new Font("Tahoma", 10)
            };
            searchTextBox.TextChanged += SearchTextBox_TextChanged;

            // Buttons
            addButton = new Button
            {
                Text = "إضافة",
                Location = new Point(950, 15),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            addButton.Click += AddButton_Click;

            editButton = new Button
            {
                Text = "تعديل",
                Location = new Point(860, 15),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat
            };
            editButton.Click += EditButton_Click;

            deleteButton = new Button
            {
                Text = "حذف",
                Location = new Point(770, 15),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            deleteButton.Click += DeleteButton_Click;

            refreshButton = new Button
            {
                Text = "تحديث",
                Location = new Point(680, 15),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            refreshButton.Click += RefreshButton_Click;

            topPanel.Controls.AddRange(new Control[] { searchLabel, searchTextBox, addButton, editButton, deleteButton, refreshButton });
            this.Controls.Add(topPanel);
        }

        private void CreateBottomPanel()
        {
            bottomPanel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Bottom,
                BackColor = Color.FromArgb(248, 249, 250)
            };

            this.Controls.Add(bottomPanel);
        }

        private void CreateCenterPanel()
        {
            centerPanel = new Panel
            {
                Dock = DockStyle.Fill
            };

            // Create DataGridView
            itemsGrid = new DataGridView
            {
                Dock = DockStyle.Left,
                Width = 800,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Tahoma", 9)
            };

            itemsGrid.SelectionChanged += ItemsGrid_SelectionChanged;

            centerPanel.Controls.Add(itemsGrid);
            this.Controls.Add(centerPanel);
        }

        private void CreateItemDetailsPanel()
        {
            itemDetailsGroup = new GroupBox
            {
                Text = "بيانات الصنف",
                Dock = DockStyle.Fill,
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Padding = new Padding(10)
            };

            // Item Code
            var itemCodeLabel = new Label { Text = "كود الصنف:", Location = new Point(500, 30), Size = new Size(80, 20) };
            itemCodeTextBox = new TextBox { Location = new Point(350, 28), Size = new Size(150, 25), ReadOnly = true };

            // Item Name
            var itemNameLabel = new Label { Text = "اسم الصنف:", Location = new Point(500, 65), Size = new Size(80, 20) };
            itemNameTextBox = new TextBox { Location = new Point(350, 63), Size = new Size(150, 25) };

            // Item Name English
            var itemNameEnLabel = new Label { Text = "الاسم بالإنجليزية:", Location = new Point(500, 100), Size = new Size(80, 20) };
            itemNameEnTextBox = new TextBox { Location = new Point(350, 98), Size = new Size(150, 25) };

            // Category
            var categoryLabel = new Label { Text = "المجموعة:", Location = new Point(500, 135), Size = new Size(80, 20) };
            categoryComboBox = new ComboBox { Location = new Point(350, 133), Size = new Size(150, 25), DropDownStyle = ComboBoxStyle.DropDownList };

            // Base Unit
            var baseUnitLabel = new Label { Text = "الوحدة الأساسية:", Location = new Point(500, 170), Size = new Size(80, 20) };
            baseUnitComboBox = new ComboBox { Location = new Point(350, 168), Size = new Size(150, 25), DropDownStyle = ComboBoxStyle.DropDownList };

            // Barcode
            var barcodeLabel = new Label { Text = "الباركود:", Location = new Point(500, 205), Size = new Size(80, 20) };
            barcodeTextBox = new TextBox { Location = new Point(350, 203), Size = new Size(120, 25) };
            generateBarcodeButton = new Button 
            { 
                Text = "توليد", 
                Location = new Point(475, 203), 
                Size = new Size(50, 25),
                BackColor = Color.FromArgb(23, 162, 184),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            generateBarcodeButton.Click += GenerateBarcodeButton_Click;

            // Description
            var descriptionLabel = new Label { Text = "الوصف:", Location = new Point(500, 240), Size = new Size(80, 20) };
            descriptionTextBox = new TextBox { Location = new Point(350, 238), Size = new Size(150, 60), Multiline = true };

            // Min Stock Level
            var minStockLabel = new Label { Text = "الحد الأدنى:", Location = new Point(500, 310), Size = new Size(80, 20) };
            minStockLevelNumeric = new NumericUpDown { Location = new Point(350, 308), Size = new Size(150, 25), Maximum = 999999999, DecimalPlaces = 3 };

            // Max Stock Level
            var maxStockLabel = new Label { Text = "الحد الأقصى:", Location = new Point(500, 345), Size = new Size(80, 20) };
            maxStockLevelNumeric = new NumericUpDown { Location = new Point(350, 343), Size = new Size(150, 25), Maximum = 999999999, DecimalPlaces = 3 };

            // Reorder Level
            var reorderLabel = new Label { Text = "نقطة إعادة الطلب:", Location = new Point(500, 380), Size = new Size(80, 20) };
            reorderLevelNumeric = new NumericUpDown { Location = new Point(350, 378), Size = new Size(150, 25), Maximum = 999999999, DecimalPlaces = 3 };

            // Checkboxes
            hasExpiryCheckBox = new CheckBox { Text = "له تاريخ انتهاء", Location = new Point(350, 415), Size = new Size(120, 20) };
            isSerialCheckBox = new CheckBox { Text = "له رقم تسلسلي", Location = new Point(350, 440), Size = new Size(120, 20) };
            isActiveCheckBox = new CheckBox { Text = "نشط", Location = new Point(350, 465), Size = new Size(60, 20), Checked = true };

            // Buttons
            saveButton = new Button
            {
                Text = "حفظ",
                Location = new Point(450, 500),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };
            saveButton.Click += SaveButton_Click;

            cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(360, 500),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };
            cancelButton.Click += CancelButton_Click;

            itemDetailsGroup.Controls.AddRange(new Control[]
            {
                itemCodeLabel, itemCodeTextBox,
                itemNameLabel, itemNameTextBox,
                itemNameEnLabel, itemNameEnTextBox,
                categoryLabel, categoryComboBox,
                baseUnitLabel, baseUnitComboBox,
                barcodeLabel, barcodeTextBox, generateBarcodeButton,
                descriptionLabel, descriptionTextBox,
                minStockLabel, minStockLevelNumeric,
                maxStockLabel, maxStockLevelNumeric,
                reorderLabel, reorderLevelNumeric,
                hasExpiryCheckBox, isSerialCheckBox, isActiveCheckBox,
                saveButton, cancelButton
            });

            centerPanel.Controls.Add(itemDetailsGroup);
        }

        private async void ItemForm_Load(object sender, EventArgs e)
        {
            try
            {
                await LoadComboBoxData();
                await LoadItems();
                SetupGridColumns();
                SetEditMode(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل نموذج الأصناف");
                MessageBox.Show("خطأ في تحميل البيانات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadComboBoxData()
        {
            try
            {
                // Load categories
                var categories = await _itemService.GetAllCategoriesAsync();
                categoryComboBox.DataSource = categories.ToList();
                categoryComboBox.DisplayMember = "CategoryName";
                categoryComboBox.ValueMember = "Id";
                categoryComboBox.SelectedIndex = -1;

                // Load units
                var units = await _itemService.GetAllUnitsAsync();
                baseUnitComboBox.DataSource = units.ToList();
                baseUnitComboBox.DisplayMember = "UnitName";
                baseUnitComboBox.ValueMember = "Id";
                baseUnitComboBox.SelectedIndex = -1;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل بيانات القوائم المنسدلة");
                throw;
            }
        }

        private async Task LoadItems()
        {
            try
            {
                var items = await _itemService.GetAllItemsAsync();
                itemsGrid.DataSource = items.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل الأصناف");
                throw;
            }
        }

        private void SetupGridColumns()
        {
            if (itemsGrid.Columns.Count > 0)
            {
                itemsGrid.Columns["Id"].Visible = false;
                itemsGrid.Columns["ItemCode"].HeaderText = "الكود";
                itemsGrid.Columns["ItemName"].HeaderText = "الاسم";
                itemsGrid.Columns["Barcode"].HeaderText = "الباركود";
                itemsGrid.Columns["IsActive"].HeaderText = "نشط";
                
                // Hide other columns
                foreach (DataGridViewColumn column in itemsGrid.Columns)
                {
                    if (!new[] { "ItemCode", "ItemName", "Barcode", "IsActive" }.Contains(column.Name))
                    {
                        column.Visible = false;
                    }
                }
            }
        }

        private void ItemsGrid_SelectionChanged(object sender, EventArgs e)
        {
            if (itemsGrid.SelectedRows.Count > 0 && !_isEditing)
            {
                var selectedItem = (Item)itemsGrid.SelectedRows[0].DataBoundItem;
                DisplayItem(selectedItem);
            }
        }

        private void DisplayItem(Item item)
        {
            _currentItem = item;
            itemCodeTextBox.Text = item.ItemCode;
            itemNameTextBox.Text = item.ItemName;
            itemNameEnTextBox.Text = item.ItemNameEn ?? "";
            
            if (item.CategoryId.HasValue)
                categoryComboBox.SelectedValue = item.CategoryId.Value;
            else
                categoryComboBox.SelectedIndex = -1;
                
            baseUnitComboBox.SelectedValue = item.BaseUnitId;
            barcodeTextBox.Text = item.Barcode ?? "";
            descriptionTextBox.Text = item.Description ?? "";
            minStockLevelNumeric.Value = item.MinStockLevel;
            maxStockLevelNumeric.Value = item.MaxStockLevel;
            reorderLevelNumeric.Value = item.ReorderLevel;
            hasExpiryCheckBox.Checked = item.HasExpiry;
            isSerialCheckBox.Checked = item.IsSerial;
            isActiveCheckBox.Checked = item.IsActive;
        }

        private void ClearItemForm()
        {
            _currentItem = null;
            itemCodeTextBox.Clear();
            itemNameTextBox.Clear();
            itemNameEnTextBox.Clear();
            categoryComboBox.SelectedIndex = -1;
            baseUnitComboBox.SelectedIndex = -1;
            barcodeTextBox.Clear();
            descriptionTextBox.Clear();
            minStockLevelNumeric.Value = 0;
            maxStockLevelNumeric.Value = 0;
            reorderLevelNumeric.Value = 0;
            hasExpiryCheckBox.Checked = false;
            isSerialCheckBox.Checked = false;
            isActiveCheckBox.Checked = true;
        }

        private void SetEditMode(bool isEditing)
        {
            _isEditing = isEditing;
            
            // Enable/disable form controls
            itemNameTextBox.ReadOnly = !isEditing;
            itemNameEnTextBox.ReadOnly = !isEditing;
            categoryComboBox.Enabled = isEditing;
            baseUnitComboBox.Enabled = isEditing;
            barcodeTextBox.ReadOnly = !isEditing;
            generateBarcodeButton.Enabled = isEditing;
            descriptionTextBox.ReadOnly = !isEditing;
            minStockLevelNumeric.Enabled = isEditing;
            maxStockLevelNumeric.Enabled = isEditing;
            reorderLevelNumeric.Enabled = isEditing;
            hasExpiryCheckBox.Enabled = isEditing;
            isSerialCheckBox.Enabled = isEditing;
            isActiveCheckBox.Enabled = isEditing;

            // Enable/disable buttons
            saveButton.Enabled = isEditing;
            cancelButton.Enabled = isEditing;
            addButton.Enabled = !isEditing;
            editButton.Enabled = !isEditing && _currentItem != null;
            deleteButton.Enabled = !isEditing && _currentItem != null;
            itemsGrid.Enabled = !isEditing;
        }

        private async void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                ClearItemForm();
                itemCodeTextBox.Text = await _itemService.GenerateItemCodeAsync();
                SetEditMode(true);
                itemNameTextBox.Focus();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة صنف جديد");
                MessageBox.Show("خطأ في إضافة صنف جديد", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (_currentItem != null)
            {
                SetEditMode(true);
                itemNameTextBox.Focus();
            }
        }

        private async void DeleteButton_Click(object sender, EventArgs e)
        {
            if (_currentItem != null)
            {
                var result = MessageBox.Show($"هل تريد حذف الصنف '{_currentItem.ItemName}'؟", 
                    "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        await _itemService.DeleteItemAsync(_currentItem.Id);
                        await LoadItems();
                        ClearItemForm();
                        MessageBox.Show("تم حذف الصنف بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "خطأ في حذف الصنف");
                        MessageBox.Show("خطأ في حذف الصنف", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            try
            {
                await LoadItems();
                ClearItemForm();
                SetEditMode(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث البيانات");
                MessageBox.Show("خطأ في تحديث البيانات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void GenerateBarcodeButton_Click(object sender, EventArgs e)
        {
            try
            {
                barcodeTextBox.Text = await _itemService.GenerateBarcodeAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد الباركود");
                MessageBox.Show("خطأ في توليد الباركود", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(itemNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم الصنف", "تحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    itemNameTextBox.Focus();
                    return;
                }

                if (baseUnitComboBox.SelectedValue == null)
                {
                    MessageBox.Show("يرجى اختيار الوحدة الأساسية", "تحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    baseUnitComboBox.Focus();
                    return;
                }

                var item = new Item
                {
                    Id = _currentItem?.Id ?? 0,
                    ItemCode = itemCodeTextBox.Text,
                    ItemName = itemNameTextBox.Text,
                    ItemNameEn = itemNameEnTextBox.Text,
                    CategoryId = categoryComboBox.SelectedValue as int?,
                    BaseUnitId = (int)baseUnitComboBox.SelectedValue,
                    Barcode = barcodeTextBox.Text,
                    Description = descriptionTextBox.Text,
                    MinStockLevel = minStockLevelNumeric.Value,
                    MaxStockLevel = maxStockLevelNumeric.Value,
                    ReorderLevel = reorderLevelNumeric.Value,
                    HasExpiry = hasExpiryCheckBox.Checked,
                    IsSerial = isSerialCheckBox.Checked,
                    IsActive = isActiveCheckBox.Checked
                };

                if (_currentItem == null)
                {
                    // Add new item
                    await _itemService.AddItemAsync(item);
                    MessageBox.Show("تم إضافة الصنف بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // Update existing item
                    await _itemService.UpdateItemAsync(item);
                    MessageBox.Show("تم تحديث الصنف بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                await LoadItems();
                SetEditMode(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ الصنف");
                MessageBox.Show("خطأ في حفظ الصنف: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            if (_currentItem != null)
            {
                DisplayItem(_currentItem);
            }
            else
            {
                ClearItemForm();
            }
            SetEditMode(false);
        }

        private async void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTextBox.Text))
                {
                    await LoadItems();
                }
                else
                {
                    var items = await _itemService.SearchItemsAsync(searchTextBox.Text);
                    itemsGrid.DataSource = items.ToList();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث");
            }
        }
    }
}
