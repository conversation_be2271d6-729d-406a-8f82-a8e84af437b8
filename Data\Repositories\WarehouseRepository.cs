using Microsoft.Extensions.Logging;
using POSSystem.Data.Interfaces;
using POSSystem.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;

namespace POSSystem.Data.Repositories
{
    public class WarehouseRepository : IWarehouseRepository
    {
        private readonly DatabaseManager _dbManager;
        private readonly ILogger<WarehouseRepository> _logger;

        public WarehouseRepository(DatabaseManager dbManager, ILogger<WarehouseRepository> logger)
        {
            _dbManager = dbManager;
            _logger = logger;
        }

        public async Task<IEnumerable<Warehouse>> GetAllAsync()
        {
            try
            {
                var query = @"
                    SELECT WarehouseID, WarehouseCode, WarehouseName, Location, Manager, Phone, IsActive
                    FROM Warehouses 
                    ORDER BY WarehouseName";

                var dataTable = await _dbManager.ExecuteQueryAsync(query);
                return MapDataTableToWarehouses(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب جميع المخازن");
                throw;
            }
        }

        public async Task<Warehouse?> GetByIdAsync(int id)
        {
            try
            {
                var query = @"
                    SELECT WarehouseID, WarehouseCode, WarehouseName, Location, Manager, Phone, IsActive
                    FROM Warehouses 
                    WHERE WarehouseID = @WarehouseID";

                var parameters = new[] { new SqlParameter("@WarehouseID", id) };
                var dataTable = await _dbManager.ExecuteQueryAsync(query, parameters);

                if (dataTable.Rows.Count == 0)
                    return null;

                return MapDataRowToWarehouse(dataTable.Rows[0]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المخزن برقم {WarehouseId}", id);
                throw;
            }
        }

        public async Task<Warehouse?> GetByCodeAsync(string warehouseCode)
        {
            try
            {
                var query = @"
                    SELECT WarehouseID, WarehouseCode, WarehouseName, Location, Manager, Phone, IsActive
                    FROM Warehouses 
                    WHERE WarehouseCode = @WarehouseCode";

                var parameters = new[] { new SqlParameter("@WarehouseCode", warehouseCode) };
                var dataTable = await _dbManager.ExecuteQueryAsync(query, parameters);

                if (dataTable.Rows.Count == 0)
                    return null;

                return MapDataRowToWarehouse(dataTable.Rows[0]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المخزن بالكود {WarehouseCode}", warehouseCode);
                throw;
            }
        }

        public async Task<int> AddAsync(Warehouse entity)
        {
            try
            {
                var query = @"
                    INSERT INTO Warehouses (WarehouseCode, WarehouseName, Location, Manager, Phone, IsActive)
                    VALUES (@WarehouseCode, @WarehouseName, @Location, @Manager, @Phone, @IsActive);
                    SELECT SCOPE_IDENTITY();";

                var parameters = new[]
                {
                    new SqlParameter("@WarehouseCode", entity.WarehouseCode),
                    new SqlParameter("@WarehouseName", entity.WarehouseName),
                    new SqlParameter("@Location", (object?)entity.Location ?? DBNull.Value),
                    new SqlParameter("@Manager", (object?)entity.Manager ?? DBNull.Value),
                    new SqlParameter("@Phone", (object?)entity.Phone ?? DBNull.Value),
                    new SqlParameter("@IsActive", entity.IsActive)
                };

                var result = await _dbManager.ExecuteScalarAsync(query, parameters);
                var warehouseId = Convert.ToInt32(result);

                _logger.LogInformation("تم إضافة المخزن {WarehouseName} بنجاح برقم {WarehouseId}", entity.WarehouseName, warehouseId);
                return warehouseId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة المخزن {WarehouseName}", entity.WarehouseName);
                throw;
            }
        }

        public async Task<bool> UpdateAsync(Warehouse entity)
        {
            try
            {
                var query = @"
                    UPDATE Warehouses 
                    SET WarehouseCode = @WarehouseCode, WarehouseName = @WarehouseName, 
                        Location = @Location, Manager = @Manager, Phone = @Phone, IsActive = @IsActive
                    WHERE WarehouseID = @WarehouseID";

                var parameters = new[]
                {
                    new SqlParameter("@WarehouseID", entity.Id),
                    new SqlParameter("@WarehouseCode", entity.WarehouseCode),
                    new SqlParameter("@WarehouseName", entity.WarehouseName),
                    new SqlParameter("@Location", (object?)entity.Location ?? DBNull.Value),
                    new SqlParameter("@Manager", (object?)entity.Manager ?? DBNull.Value),
                    new SqlParameter("@Phone", (object?)entity.Phone ?? DBNull.Value),
                    new SqlParameter("@IsActive", entity.IsActive)
                };

                var rowsAffected = await _dbManager.ExecuteNonQueryAsync(query, parameters);
                var success = rowsAffected > 0;

                if (success)
                    _logger.LogInformation("تم تحديث المخزن {WarehouseName} بنجاح", entity.WarehouseName);

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث المخزن {WarehouseName}", entity.WarehouseName);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                var query = "UPDATE Warehouses SET IsActive = 0 WHERE WarehouseID = @WarehouseID";
                var parameters = new[] { new SqlParameter("@WarehouseID", id) };

                var rowsAffected = await _dbManager.ExecuteNonQueryAsync(query, parameters);
                var success = rowsAffected > 0;

                if (success)
                    _logger.LogInformation("تم حذف المخزن برقم {WarehouseId} بنجاح", id);

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف المخزن برقم {WarehouseId}", id);
                throw;
            }
        }

        public async Task<bool> ExistsAsync(int id)
        {
            try
            {
                var query = "SELECT COUNT(*) FROM Warehouses WHERE WarehouseID = @WarehouseID";
                var parameters = new[] { new SqlParameter("@WarehouseID", id) };

                var result = await _dbManager.ExecuteScalarAsync(query, parameters);
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من وجود المخزن برقم {WarehouseId}", id);
                throw;
            }
        }

        public async Task<bool> IsCodeExistsAsync(string warehouseCode, int? excludeId = null)
        {
            try
            {
                var query = "SELECT COUNT(*) FROM Warehouses WHERE WarehouseCode = @WarehouseCode";
                var parameters = new List<SqlParameter> { new("@WarehouseCode", warehouseCode) };

                if (excludeId.HasValue)
                {
                    query += " AND WarehouseID != @ExcludeId";
                    parameters.Add(new SqlParameter("@ExcludeId", excludeId.Value));
                }

                var result = await _dbManager.ExecuteScalarAsync(query, parameters.ToArray());
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من وجود كود المخزن {WarehouseCode}", warehouseCode);
                throw;
            }
        }

        public async Task<IEnumerable<Warehouse>> GetActiveWarehousesAsync()
        {
            try
            {
                var query = @"
                    SELECT WarehouseID, WarehouseCode, WarehouseName, Location, Manager, Phone, IsActive
                    FROM Warehouses 
                    WHERE IsActive = 1
                    ORDER BY WarehouseName";

                var dataTable = await _dbManager.ExecuteQueryAsync(query);
                return MapDataTableToWarehouses(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المخازن النشطة");
                throw;
            }
        }

        public async Task<IEnumerable<Warehouse>> SearchAsync(string searchTerm)
        {
            try
            {
                var query = @"
                    SELECT WarehouseID, WarehouseCode, WarehouseName, Location, Manager, Phone, IsActive
                    FROM Warehouses 
                    WHERE WarehouseCode LIKE @SearchTerm 
                       OR WarehouseName LIKE @SearchTerm 
                       OR Location LIKE @SearchTerm
                    ORDER BY WarehouseName";

                var parameters = new[] { new SqlParameter("@SearchTerm", $"%{searchTerm}%") };
                var dataTable = await _dbManager.ExecuteQueryAsync(query, parameters);
                return MapDataTableToWarehouses(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن المخازن بالمصطلح {SearchTerm}", searchTerm);
                throw;
            }
        }

        private static IEnumerable<Warehouse> MapDataTableToWarehouses(DataTable dataTable)
        {
            var warehouses = new List<Warehouse>();
            foreach (DataRow row in dataTable.Rows)
            {
                warehouses.Add(MapDataRowToWarehouse(row));
            }
            return warehouses;
        }

        private static Warehouse MapDataRowToWarehouse(DataRow row)
        {
            return new Warehouse
            {
                Id = Convert.ToInt32(row["WarehouseID"]),
                WarehouseCode = row["WarehouseCode"].ToString()!,
                WarehouseName = row["WarehouseName"].ToString()!,
                Location = row["Location"] as string,
                Manager = row["Manager"] as string,
                Phone = row["Phone"] as string,
                IsActive = Convert.ToBoolean(row["IsActive"])
            };
        }
    }
}
