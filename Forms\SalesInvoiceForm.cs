using Microsoft.Extensions.Logging;
using POSSystem.Models;
using POSSystem.Services;
using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace POSSystem.Forms
{
    public partial class SalesInvoiceForm : Form
    {
        private readonly ISalesService _salesService;
        private readonly ILogger<SalesInvoiceForm> _logger;

        // Controls
        private Panel topPanel;
        private Panel bottomPanel;
        private Panel centerPanel;
        private DataGridView invoicesGrid;
        private Button addButton;
        private Button editButton;
        private Button deleteButton;
        private Button postButton;
        private Button refreshButton;
        private TextBox searchTextBox;
        private Label searchLabel;

        // Invoice details panel
        private GroupBox invoiceDetailsGroup;
        private TextBox invoiceNumberTextBox;
        private DateTimePicker invoiceDatePicker;
        private ComboBox customerComboBox;
        private ComboBox warehouseComboBox;
        private NumericUpDown subTotalNumeric;
        private NumericUpDown discountAmountNumeric;
        private NumericUpDown discountPercentNumeric;
        private NumericUpDown taxAmountNumeric;
        private NumericUpDown taxPercentNumeric;
        private NumericUpDown totalAmountNumeric;
        private NumericUpDown paidAmountNumeric;
        private NumericUpDown remainingAmountNumeric;
        private ComboBox paymentStatusComboBox;
        private ComboBox statusComboBox;
        private DateTimePicker dueDatePicker;
        private TextBox notesTextBox;
        private Button saveButton;
        private Button cancelButton;

        // Invoice details grid
        private DataGridView invoiceDetailsGrid;
        private Button addItemButton;
        private Button removeItemButton;

        private SalesInvoice? _currentInvoice;
        private bool _isEditing = false;

        public SalesInvoiceForm(ISalesService salesService, ILogger<SalesInvoiceForm> logger)
        {
            _salesService = salesService;
            _logger = logger;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "فواتير البيع";
            this.Size = new Size(1600, 900);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateTopPanel();
            CreateBottomPanel();
            CreateCenterPanel();
            CreateInvoiceDetailsPanel();

            this.Load += SalesInvoiceForm_Load;

            this.ResumeLayout(false);
        }

        private void CreateTopPanel()
        {
            topPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(248, 249, 250)
            };

            // Search controls
            searchLabel = new Label
            {
                Text = "البحث:",
                Location = new Point(1450, 20),
                Size = new Size(50, 20),
                Font = new Font("Tahoma", 10)
            };

            searchTextBox = new TextBox
            {
                Location = new Point(1250, 18),
                Size = new Size(200, 25),
                Font = new Font("Tahoma", 10)
            };
            searchTextBox.TextChanged += SearchTextBox_TextChanged;

            // Buttons
            addButton = new Button
            {
                Text = "إضافة",
                Location = new Point(1150, 15),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            addButton.Click += AddButton_Click;

            editButton = new Button
            {
                Text = "تعديل",
                Location = new Point(1060, 15),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat
            };
            editButton.Click += EditButton_Click;

            deleteButton = new Button
            {
                Text = "حذف",
                Location = new Point(970, 15),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            deleteButton.Click += DeleteButton_Click;

            postButton = new Button
            {
                Text = "ترحيل",
                Location = new Point(880, 15),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(23, 162, 184),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            postButton.Click += PostButton_Click;

            refreshButton = new Button
            {
                Text = "تحديث",
                Location = new Point(790, 15),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            refreshButton.Click += RefreshButton_Click;

            topPanel.Controls.AddRange(new Control[] { searchLabel, searchTextBox, addButton, editButton, deleteButton, postButton, refreshButton });
            this.Controls.Add(topPanel);
        }

        private void CreateBottomPanel()
        {
            bottomPanel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Bottom,
                BackColor = Color.FromArgb(248, 249, 250)
            };

            this.Controls.Add(bottomPanel);
        }

        private void CreateCenterPanel()
        {
            centerPanel = new Panel
            {
                Dock = DockStyle.Fill
            };

            // Create DataGridView for invoices
            invoicesGrid = new DataGridView
            {
                Dock = DockStyle.Left,
                Width = 800,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Tahoma", 9)
            };

            invoicesGrid.SelectionChanged += InvoicesGrid_SelectionChanged;

            centerPanel.Controls.Add(invoicesGrid);
            this.Controls.Add(centerPanel);
        }

        private void CreateInvoiceDetailsPanel()
        {
            invoiceDetailsGroup = new GroupBox
            {
                Text = "بيانات الفاتورة",
                Dock = DockStyle.Fill,
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Padding = new Padding(10)
            };

            // Invoice Number
            var invoiceNumberLabel = new Label { Text = "رقم الفاتورة:", Location = new Point(700, 30), Size = new Size(80, 20) };
            invoiceNumberTextBox = new TextBox { Location = new Point(550, 28), Size = new Size(150, 25), ReadOnly = true };

            // Invoice Date
            var invoiceDateLabel = new Label { Text = "تاريخ الفاتورة:", Location = new Point(700, 65), Size = new Size(80, 20) };
            invoiceDatePicker = new DateTimePicker { Location = new Point(550, 63), Size = new Size(150, 25) };

            // Customer
            var customerLabel = new Label { Text = "العميل:", Location = new Point(700, 100), Size = new Size(80, 20) };
            customerComboBox = new ComboBox { Location = new Point(550, 98), Size = new Size(150, 25), DropDownStyle = ComboBoxStyle.DropDownList };

            // Warehouse
            var warehouseLabel = new Label { Text = "المخزن:", Location = new Point(700, 135), Size = new Size(80, 20) };
            warehouseComboBox = new ComboBox { Location = new Point(550, 133), Size = new Size(150, 25), DropDownStyle = ComboBoxStyle.DropDownList };

            // Sub Total
            var subTotalLabel = new Label { Text = "المجموع الفرعي:", Location = new Point(700, 170), Size = new Size(80, 20) };
            subTotalNumeric = new NumericUpDown { Location = new Point(550, 168), Size = new Size(150, 25), Maximum = *********, DecimalPlaces = 2, ReadOnly = true };

            // Discount Amount
            var discountAmountLabel = new Label { Text = "مبلغ الخصم:", Location = new Point(700, 205), Size = new Size(80, 20) };
            discountAmountNumeric = new NumericUpDown { Location = new Point(550, 203), Size = new Size(150, 25), Maximum = *********, DecimalPlaces = 2 };

            // Tax Amount
            var taxAmountLabel = new Label { Text = "مبلغ الضريبة:", Location = new Point(700, 240), Size = new Size(80, 20) };
            taxAmountNumeric = new NumericUpDown { Location = new Point(550, 238), Size = new Size(150, 25), Maximum = *********, DecimalPlaces = 2 };

            // Total Amount
            var totalAmountLabel = new Label { Text = "الإجمالي:", Location = new Point(700, 275), Size = new Size(80, 20) };
            totalAmountNumeric = new NumericUpDown { Location = new Point(550, 273), Size = new Size(150, 25), Maximum = *********, DecimalPlaces = 2, ReadOnly = true };

            // Paid Amount
            var paidAmountLabel = new Label { Text = "المبلغ المدفوع:", Location = new Point(700, 310), Size = new Size(80, 20) };
            paidAmountNumeric = new NumericUpDown { Location = new Point(550, 308), Size = new Size(150, 25), Maximum = *********, DecimalPlaces = 2 };

            // Payment Status
            var paymentStatusLabel = new Label { Text = "حالة الدفع:", Location = new Point(700, 345), Size = new Size(80, 20) };
            paymentStatusComboBox = new ComboBox { Location = new Point(550, 343), Size = new Size(150, 25), DropDownStyle = ComboBoxStyle.DropDownList };
            paymentStatusComboBox.Items.AddRange(new[] { "Pending", "Partial", "Paid" });

            // Status
            var statusLabel = new Label { Text = "الحالة:", Location = new Point(700, 380), Size = new Size(80, 20) };
            statusComboBox = new ComboBox { Location = new Point(550, 378), Size = new Size(150, 25), DropDownStyle = ComboBoxStyle.DropDownList };
            statusComboBox.Items.AddRange(new[] { "Draft", "Posted", "Cancelled" });

            // Notes
            var notesLabel = new Label { Text = "ملاحظات:", Location = new Point(700, 415), Size = new Size(80, 20) };
            notesTextBox = new TextBox { Location = new Point(550, 413), Size = new Size(150, 60), Multiline = true };

            // Invoice Details Grid
            invoiceDetailsGrid = new DataGridView
            {
                Location = new Point(20, 480),
                Size = new Size(750, 200),
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Tahoma", 9)
            };

            // Item buttons
            addItemButton = new Button
            {
                Text = "إضافة صنف",
                Location = new Point(680, 690),
                Size = new Size(90, 30),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };

            removeItemButton = new Button
            {
                Text = "حذف صنف",
                Location = new Point(580, 690),
                Size = new Size(90, 30),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };

            // Buttons
            saveButton = new Button
            {
                Text = "حفظ",
                Location = new Point(650, 730),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };
            saveButton.Click += SaveButton_Click;

            cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(560, 730),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };
            cancelButton.Click += CancelButton_Click;

            invoiceDetailsGroup.Controls.AddRange(new Control[]
            {
                invoiceNumberLabel, invoiceNumberTextBox,
                invoiceDateLabel, invoiceDatePicker,
                customerLabel, customerComboBox,
                warehouseLabel, warehouseComboBox,
                subTotalLabel, subTotalNumeric,
                discountAmountLabel, discountAmountNumeric,
                taxAmountLabel, taxAmountNumeric,
                totalAmountLabel, totalAmountNumeric,
                paidAmountLabel, paidAmountNumeric,
                paymentStatusLabel, paymentStatusComboBox,
                statusLabel, statusComboBox,
                notesLabel, notesTextBox,
                invoiceDetailsGrid,
                addItemButton, removeItemButton,
                saveButton, cancelButton
            });

            centerPanel.Controls.Add(invoiceDetailsGroup);
        }

        private async void SalesInvoiceForm_Load(object sender, EventArgs e)
        {
            try
            {
                await LoadComboBoxData();
                await LoadInvoices();
                SetupGridColumns();
                SetEditMode(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل نموذج فواتير البيع");
                MessageBox.Show("خطأ في تحميل البيانات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadComboBoxData()
        {
            try
            {
                // Load customers
                var customers = await _salesService.GetActiveCustomersAsync();
                customerComboBox.DataSource = customers.ToList();
                customerComboBox.DisplayMember = "CustomerName";
                customerComboBox.ValueMember = "Id";
                customerComboBox.SelectedIndex = -1;

                // Load warehouses
                var warehouses = await _salesService.GetActiveWarehousesAsync();
                warehouseComboBox.DataSource = warehouses.ToList();
                warehouseComboBox.DisplayMember = "WarehouseName";
                warehouseComboBox.ValueMember = "Id";
                warehouseComboBox.SelectedIndex = -1;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل بيانات القوائم المنسدلة");
                throw;
            }
        }

        private async Task LoadInvoices()
        {
            try
            {
                var invoices = await _salesService.GetAllSalesInvoicesAsync();
                invoicesGrid.DataSource = invoices.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل فواتير البيع");
                throw;
            }
        }

        private void SetupGridColumns()
        {
            if (invoicesGrid.Columns.Count > 0)
            {
                invoicesGrid.Columns["Id"].Visible = false;
                invoicesGrid.Columns["InvoiceNumber"].HeaderText = "رقم الفاتورة";
                invoicesGrid.Columns["InvoiceDate"].HeaderText = "التاريخ";
                invoicesGrid.Columns["TotalAmount"].HeaderText = "الإجمالي";
                invoicesGrid.Columns["Status"].HeaderText = "الحالة";
                invoicesGrid.Columns["PaymentStatus"].HeaderText = "حالة الدفع";
                
                // Hide other columns
                foreach (DataGridViewColumn column in invoicesGrid.Columns)
                {
                    if (!new[] { "InvoiceNumber", "InvoiceDate", "TotalAmount", "Status", "PaymentStatus" }.Contains(column.Name))
                    {
                        column.Visible = false;
                    }
                }
            }
        }

        private void InvoicesGrid_SelectionChanged(object sender, EventArgs e)
        {
            if (invoicesGrid.SelectedRows.Count > 0 && !_isEditing)
            {
                var selectedInvoice = (SalesInvoice)invoicesGrid.SelectedRows[0].DataBoundItem;
                DisplayInvoice(selectedInvoice);
            }
        }

        private void DisplayInvoice(SalesInvoice invoice)
        {
            _currentInvoice = invoice;
            invoiceNumberTextBox.Text = invoice.InvoiceNumber;
            invoiceDatePicker.Value = invoice.InvoiceDate;
            customerComboBox.SelectedValue = invoice.CustomerId;
            warehouseComboBox.SelectedValue = invoice.WarehouseId;
            subTotalNumeric.Value = invoice.SubTotal;
            discountAmountNumeric.Value = invoice.DiscountAmount;
            taxAmountNumeric.Value = invoice.TaxAmount;
            totalAmountNumeric.Value = invoice.TotalAmount;
            paidAmountNumeric.Value = invoice.PaidAmount;
            paymentStatusComboBox.SelectedItem = invoice.PaymentStatus;
            statusComboBox.SelectedItem = invoice.Status;
            notesTextBox.Text = invoice.Notes ?? "";
        }

        private void ClearInvoiceForm()
        {
            _currentInvoice = null;
            invoiceNumberTextBox.Clear();
            invoiceDatePicker.Value = DateTime.Today;
            customerComboBox.SelectedIndex = -1;
            warehouseComboBox.SelectedIndex = -1;
            subTotalNumeric.Value = 0;
            discountAmountNumeric.Value = 0;
            taxAmountNumeric.Value = 0;
            totalAmountNumeric.Value = 0;
            paidAmountNumeric.Value = 0;
            paymentStatusComboBox.SelectedIndex = 0;
            statusComboBox.SelectedIndex = 0;
            notesTextBox.Clear();
            invoiceDetailsGrid.DataSource = null;
        }

        private void SetEditMode(bool isEditing)
        {
            _isEditing = isEditing;
            
            // Enable/disable form controls
            invoiceDatePicker.Enabled = isEditing;
            customerComboBox.Enabled = isEditing;
            warehouseComboBox.Enabled = isEditing;
            discountAmountNumeric.Enabled = isEditing;
            taxAmountNumeric.Enabled = isEditing;
            paidAmountNumeric.Enabled = isEditing;
            paymentStatusComboBox.Enabled = isEditing;
            statusComboBox.Enabled = isEditing;
            notesTextBox.ReadOnly = !isEditing;
            addItemButton.Enabled = isEditing;
            removeItemButton.Enabled = isEditing;

            // Enable/disable buttons
            saveButton.Enabled = isEditing;
            cancelButton.Enabled = isEditing;
            addButton.Enabled = !isEditing;
            editButton.Enabled = !isEditing && _currentInvoice != null && _currentInvoice.Status != "Posted";
            deleteButton.Enabled = !isEditing && _currentInvoice != null && _currentInvoice.Status != "Posted";
            postButton.Enabled = !isEditing && _currentInvoice != null && _currentInvoice.Status == "Draft";
            invoicesGrid.Enabled = !isEditing;
        }

        private async void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                ClearInvoiceForm();
                invoiceNumberTextBox.Text = await _salesService.GenerateSalesInvoiceNumberAsync();
                SetEditMode(true);
                customerComboBox.Focus();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة فاتورة جديدة");
                MessageBox.Show("خطأ في إضافة فاتورة جديدة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (_currentInvoice != null && _currentInvoice.Status != "Posted")
            {
                SetEditMode(true);
                customerComboBox.Focus();
            }
        }

        private async void DeleteButton_Click(object sender, EventArgs e)
        {
            if (_currentInvoice != null && _currentInvoice.Status != "Posted")
            {
                var result = MessageBox.Show($"هل تريد حذف الفاتورة '{_currentInvoice.InvoiceNumber}'؟", 
                    "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        await _salesService.DeleteSalesInvoiceAsync(_currentInvoice.Id);
                        await LoadInvoices();
                        ClearInvoiceForm();
                        MessageBox.Show("تم حذف الفاتورة بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "خطأ في حذف الفاتورة");
                        MessageBox.Show("خطأ في حذف الفاتورة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private async void PostButton_Click(object sender, EventArgs e)
        {
            if (_currentInvoice != null && _currentInvoice.Status == "Draft")
            {
                var result = MessageBox.Show($"هل تريد ترحيل الفاتورة '{_currentInvoice.InvoiceNumber}'؟", 
                    "تأكيد الترحيل", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        await _salesService.PostSalesInvoiceAsync(_currentInvoice.Id, CurrentUser.User?.Id ?? 0);
                        await LoadInvoices();
                        DisplayInvoice(_currentInvoice);
                        MessageBox.Show("تم ترحيل الفاتورة بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "خطأ في ترحيل الفاتورة");
                        MessageBox.Show("خطأ في ترحيل الفاتورة: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            try
            {
                await LoadInvoices();
                ClearInvoiceForm();
                SetEditMode(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث البيانات");
                MessageBox.Show("خطأ في تحديث البيانات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (customerComboBox.SelectedValue == null)
                {
                    MessageBox.Show("يرجى اختيار العميل", "تحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    customerComboBox.Focus();
                    return;
                }

                if (warehouseComboBox.SelectedValue == null)
                {
                    MessageBox.Show("يرجى اختيار المخزن", "تحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    warehouseComboBox.Focus();
                    return;
                }

                var invoice = new SalesInvoice
                {
                    Id = _currentInvoice?.Id ?? 0,
                    InvoiceNumber = invoiceNumberTextBox.Text,
                    InvoiceDate = invoiceDatePicker.Value,
                    CustomerId = (int)customerComboBox.SelectedValue,
                    WarehouseId = (int)warehouseComboBox.SelectedValue,
                    SubTotal = subTotalNumeric.Value,
                    DiscountAmount = discountAmountNumeric.Value,
                    TaxAmount = taxAmountNumeric.Value,
                    TotalAmount = totalAmountNumeric.Value,
                    PaidAmount = paidAmountNumeric.Value,
                    PaymentStatus = paymentStatusComboBox.SelectedItem?.ToString() ?? "Pending",
                    Status = statusComboBox.SelectedItem?.ToString() ?? "Draft",
                    Notes = notesTextBox.Text
                };

                if (_currentInvoice == null)
                {
                    // Add new invoice
                    await _salesService.AddSalesInvoiceAsync(invoice);
                    MessageBox.Show("تم إضافة الفاتورة بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // Update existing invoice
                    await _salesService.UpdateSalesInvoiceAsync(invoice);
                    MessageBox.Show("تم تحديث الفاتورة بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                await LoadInvoices();
                SetEditMode(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ الفاتورة");
                MessageBox.Show("خطأ في حفظ الفاتورة: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            if (_currentInvoice != null)
            {
                DisplayInvoice(_currentInvoice);
            }
            else
            {
                ClearInvoiceForm();
            }
            SetEditMode(false);
        }

        private async void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTextBox.Text))
                {
                    await LoadInvoices();
                }
                else
                {
                    var invoices = await _salesService.SearchSalesInvoicesAsync(searchTextBox.Text);
                    invoicesGrid.DataSource = invoices.ToList();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث");
            }
        }
    }
}
