using Microsoft.Extensions.Logging;
using POSSystem.Data.Interfaces;
using POSSystem.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;

namespace POSSystem.Data.Repositories
{
    public class UnitRepository : IUnitRepository
    {
        private readonly DatabaseManager _dbManager;
        private readonly ILogger<UnitRepository> _logger;

        public UnitRepository(DatabaseManager dbManager, ILogger<UnitRepository> logger)
        {
            _dbManager = dbManager;
            _logger = logger;
        }

        public async Task<IEnumerable<Unit>> GetAllAsync()
        {
            try
            {
                var query = @"
                    SELECT UnitID, UnitCode, UnitName, IsActive
                    FROM Units 
                    ORDER BY UnitName";

                var dataTable = await _dbManager.ExecuteQueryAsync(query);
                return MapDataTableToUnits(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب جميع الوحدات");
                throw;
            }
        }

        public async Task<Unit?> GetByIdAsync(int id)
        {
            try
            {
                var query = @"
                    SELECT UnitID, UnitCode, UnitName, IsActive
                    FROM Units 
                    WHERE UnitID = @UnitID";

                var parameters = new[] { new SqlParameter("@UnitID", id) };
                var dataTable = await _dbManager.ExecuteQueryAsync(query, parameters);

                if (dataTable.Rows.Count == 0)
                    return null;

                return MapDataRowToUnit(dataTable.Rows[0]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الوحدة برقم {UnitId}", id);
                throw;
            }
        }

        public async Task<Unit?> GetByCodeAsync(string unitCode)
        {
            try
            {
                var query = @"
                    SELECT UnitID, UnitCode, UnitName, IsActive
                    FROM Units 
                    WHERE UnitCode = @UnitCode";

                var parameters = new[] { new SqlParameter("@UnitCode", unitCode) };
                var dataTable = await _dbManager.ExecuteQueryAsync(query, parameters);

                if (dataTable.Rows.Count == 0)
                    return null;

                return MapDataRowToUnit(dataTable.Rows[0]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الوحدة بالكود {UnitCode}", unitCode);
                throw;
            }
        }

        public async Task<int> AddAsync(Unit entity)
        {
            try
            {
                var query = @"
                    INSERT INTO Units (UnitCode, UnitName, IsActive)
                    VALUES (@UnitCode, @UnitName, @IsActive);
                    SELECT SCOPE_IDENTITY();";

                var parameters = new[]
                {
                    new SqlParameter("@UnitCode", entity.UnitCode),
                    new SqlParameter("@UnitName", entity.UnitName),
                    new SqlParameter("@IsActive", entity.IsActive)
                };

                var result = await _dbManager.ExecuteScalarAsync(query, parameters);
                var unitId = Convert.ToInt32(result);

                _logger.LogInformation("تم إضافة الوحدة {UnitName} بنجاح برقم {UnitId}", entity.UnitName, unitId);
                return unitId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة الوحدة {UnitName}", entity.UnitName);
                throw;
            }
        }

        public async Task<bool> UpdateAsync(Unit entity)
        {
            try
            {
                var query = @"
                    UPDATE Units 
                    SET UnitCode = @UnitCode, UnitName = @UnitName, IsActive = @IsActive
                    WHERE UnitID = @UnitID";

                var parameters = new[]
                {
                    new SqlParameter("@UnitID", entity.Id),
                    new SqlParameter("@UnitCode", entity.UnitCode),
                    new SqlParameter("@UnitName", entity.UnitName),
                    new SqlParameter("@IsActive", entity.IsActive)
                };

                var rowsAffected = await _dbManager.ExecuteNonQueryAsync(query, parameters);
                var success = rowsAffected > 0;

                if (success)
                    _logger.LogInformation("تم تحديث الوحدة {UnitName} بنجاح", entity.UnitName);

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الوحدة {UnitName}", entity.UnitName);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                var query = "UPDATE Units SET IsActive = 0 WHERE UnitID = @UnitID";
                var parameters = new[] { new SqlParameter("@UnitID", id) };

                var rowsAffected = await _dbManager.ExecuteNonQueryAsync(query, parameters);
                var success = rowsAffected > 0;

                if (success)
                    _logger.LogInformation("تم حذف الوحدة برقم {UnitId} بنجاح", id);

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الوحدة برقم {UnitId}", id);
                throw;
            }
        }

        public async Task<bool> ExistsAsync(int id)
        {
            try
            {
                var query = "SELECT COUNT(*) FROM Units WHERE UnitID = @UnitID";
                var parameters = new[] { new SqlParameter("@UnitID", id) };

                var result = await _dbManager.ExecuteScalarAsync(query, parameters);
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من وجود الوحدة برقم {UnitId}", id);
                throw;
            }
        }

        public async Task<bool> IsCodeExistsAsync(string unitCode, int? excludeId = null)
        {
            try
            {
                var query = "SELECT COUNT(*) FROM Units WHERE UnitCode = @UnitCode";
                var parameters = new List<SqlParameter> { new("@UnitCode", unitCode) };

                if (excludeId.HasValue)
                {
                    query += " AND UnitID != @ExcludeId";
                    parameters.Add(new SqlParameter("@ExcludeId", excludeId.Value));
                }

                var result = await _dbManager.ExecuteScalarAsync(query, parameters.ToArray());
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من وجود كود الوحدة {UnitCode}", unitCode);
                throw;
            }
        }

        public async Task<IEnumerable<Unit>> GetActiveUnitsAsync()
        {
            try
            {
                var query = @"
                    SELECT UnitID, UnitCode, UnitName, IsActive
                    FROM Units 
                    WHERE IsActive = 1
                    ORDER BY UnitName";

                var dataTable = await _dbManager.ExecuteQueryAsync(query);
                return MapDataTableToUnits(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الوحدات النشطة");
                throw;
            }
        }

        public async Task<IEnumerable<Unit>> SearchAsync(string searchTerm)
        {
            try
            {
                var query = @"
                    SELECT UnitID, UnitCode, UnitName, IsActive
                    FROM Units 
                    WHERE UnitCode LIKE @SearchTerm 
                       OR UnitName LIKE @SearchTerm
                    ORDER BY UnitName";

                var parameters = new[] { new SqlParameter("@SearchTerm", $"%{searchTerm}%") };
                var dataTable = await _dbManager.ExecuteQueryAsync(query, parameters);
                return MapDataTableToUnits(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن الوحدات بالمصطلح {SearchTerm}", searchTerm);
                throw;
            }
        }

        private static IEnumerable<Unit> MapDataTableToUnits(DataTable dataTable)
        {
            var units = new List<Unit>();
            foreach (DataRow row in dataTable.Rows)
            {
                units.Add(MapDataRowToUnit(row));
            }
            return units;
        }

        private static Unit MapDataRowToUnit(DataRow row)
        {
            return new Unit
            {
                Id = Convert.ToInt32(row["UnitID"]),
                UnitCode = row["UnitCode"].ToString()!,
                UnitName = row["UnitName"].ToString()!,
                IsActive = Convert.ToBoolean(row["IsActive"])
            };
        }
    }
}
