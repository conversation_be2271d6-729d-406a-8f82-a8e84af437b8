using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using POSSystem.Services;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace POSSystem.Forms
{
    public partial class LoginForm : Form
    {
        private readonly IUserService _userService;
        private readonly ILogger<LoginForm> _logger;
        private readonly IServiceProvider _serviceProvider;

        // Controls
        private Panel mainPanel;
        private Panel loginPanel;
        private Label titleLabel;
        private Label usernameLabel;
        private TextBox usernameTextBox;
        private Label passwordLabel;
        private TextBox passwordTextBox;
        private Button loginButton;
        private Button exitButton;
        private Label statusLabel;
        private PictureBox logoBox;

        public LoginForm(IUserService userService, ILogger<LoginForm> logger, IServiceProvider serviceProvider)
        {
            _userService = userService;
            _logger = logger;
            _serviceProvider = serviceProvider;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "تسجيل الدخول - نظام إدارة المبيعات والمشتريات";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Main panel
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(240, 240, 240)
            };
            this.Controls.Add(mainPanel);

            // Login panel
            loginPanel = new Panel
            {
                Size = new Size(350, 300),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            loginPanel.Location = new Point(
                (mainPanel.Width - loginPanel.Width) / 2,
                (mainPanel.Height - loginPanel.Height) / 2
            );
            mainPanel.Controls.Add(loginPanel);

            // Logo
            logoBox = new PictureBox
            {
                Size = new Size(64, 64),
                Location = new Point((loginPanel.Width - 64) / 2, 20),
                BackColor = Color.LightBlue,
                BorderStyle = BorderStyle.FixedSingle
            };
            loginPanel.Controls.Add(logoBox);

            // Title
            titleLabel = new Label
            {
                Text = "نظام إدارة المبيعات والمشتريات",
                Font = new Font("Tahoma", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 51),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(300, 30),
                Location = new Point(25, 95)
            };
            loginPanel.Controls.Add(titleLabel);

            // Username label
            usernameLabel = new Label
            {
                Text = "اسم المستخدم:",
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(51, 51, 51),
                Size = new Size(100, 20),
                Location = new Point(250, 140),
                TextAlign = ContentAlignment.MiddleRight
            };
            loginPanel.Controls.Add(usernameLabel);

            // Username textbox
            usernameTextBox = new TextBox
            {
                Font = new Font("Tahoma", 10),
                Size = new Size(200, 25),
                Location = new Point(40, 138),
                RightToLeft = RightToLeft.No
            };
            loginPanel.Controls.Add(usernameTextBox);

            // Password label
            passwordLabel = new Label
            {
                Text = "كلمة المرور:",
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(51, 51, 51),
                Size = new Size(100, 20),
                Location = new Point(250, 175),
                TextAlign = ContentAlignment.MiddleRight
            };
            loginPanel.Controls.Add(passwordLabel);

            // Password textbox
            passwordTextBox = new TextBox
            {
                Font = new Font("Tahoma", 10),
                Size = new Size(200, 25),
                Location = new Point(40, 173),
                UseSystemPasswordChar = true,
                RightToLeft = RightToLeft.No
            };
            loginPanel.Controls.Add(passwordTextBox);

            // Login button
            loginButton = new Button
            {
                Text = "دخول",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Size = new Size(90, 35),
                Location = new Point(150, 220),
                BackColor = Color.FromArgb(0, 123, 255),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            loginButton.FlatAppearance.BorderSize = 0;
            loginButton.Click += LoginButton_Click;
            loginPanel.Controls.Add(loginButton);

            // Exit button
            exitButton = new Button
            {
                Text = "خروج",
                Font = new Font("Tahoma", 10),
                Size = new Size(90, 35),
                Location = new Point(250, 220),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            exitButton.FlatAppearance.BorderSize = 0;
            exitButton.Click += ExitButton_Click;
            loginPanel.Controls.Add(exitButton);

            // Status label
            statusLabel = new Label
            {
                Text = "",
                Font = new Font("Tahoma", 9),
                ForeColor = Color.Red,
                Size = new Size(300, 20),
                Location = new Point(25, 265),
                TextAlign = ContentAlignment.MiddleCenter
            };
            loginPanel.Controls.Add(statusLabel);

            // Event handlers
            this.Load += LoginForm_Load;
            this.KeyDown += LoginForm_KeyDown;
            usernameTextBox.KeyDown += TextBox_KeyDown;
            passwordTextBox.KeyDown += TextBox_KeyDown;

            // Set tab order
            usernameTextBox.TabIndex = 0;
            passwordTextBox.TabIndex = 1;
            loginButton.TabIndex = 2;
            exitButton.TabIndex = 3;

            this.ResumeLayout(false);
        }

        private void LoginForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Set default values for testing
                usernameTextBox.Text = "admin";
                passwordTextBox.Text = "admin123";
                
                usernameTextBox.Focus();
                statusLabel.Text = "أدخل اسم المستخدم وكلمة المرور";
                statusLabel.ForeColor = Color.FromArgb(108, 117, 125);

                _logger.LogInformation("تم فتح نموذج تسجيل الدخول");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل نموذج تسجيل الدخول");
                ShowError("خطأ في تحميل النموذج");
            }
        }

        private async void LoginButton_Click(object sender, EventArgs e)
        {
            try
            {
                await PerformLogin();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في عملية تسجيل الدخول");
                ShowError("خطأ في عملية تسجيل الدخول");
            }
        }

        private void ExitButton_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private void LoginForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                Application.Exit();
            }
        }

        private async void TextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (sender == usernameTextBox)
                {
                    passwordTextBox.Focus();
                }
                else if (sender == passwordTextBox)
                {
                    await PerformLogin();
                }
            }
        }

        private async Task PerformLogin()
        {
            try
            {
                // Clear previous status
                statusLabel.Text = "";

                // Validate input
                if (string.IsNullOrWhiteSpace(usernameTextBox.Text))
                {
                    ShowError("يرجى إدخال اسم المستخدم");
                    usernameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(passwordTextBox.Text))
                {
                    ShowError("يرجى إدخال كلمة المرور");
                    passwordTextBox.Focus();
                    return;
                }

                // Disable controls during login
                SetControlsEnabled(false);
                statusLabel.Text = "جاري التحقق...";
                statusLabel.ForeColor = Color.FromArgb(108, 117, 125);

                // Validate credentials
                var isValid = await _userService.ValidatePasswordAsync(usernameTextBox.Text, passwordTextBox.Text);

                if (isValid)
                {
                    // Get user details
                    var user = await _userService.GetUserByUsernameAsync(usernameTextBox.Text);
                    if (user != null)
                    {
                        // Update last login
                        await _userService.UpdateLastLoginAsync(user.Id);

                        // Store current user in session (you might want to use a session manager)
                        CurrentUser.User = user;

                        _logger.LogInformation("تم تسجيل دخول المستخدم {Username} بنجاح", user.Username);

                        // Open main form
                        var mainForm = _serviceProvider.GetRequiredService<MainForm>();
                        this.Hide();
                        mainForm.ShowDialog();
                        this.Close();
                    }
                    else
                    {
                        ShowError("خطأ في جلب بيانات المستخدم");
                    }
                }
                else
                {
                    ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
                    passwordTextBox.Clear();
                    usernameTextBox.Focus();
                    _logger.LogWarning("محاولة دخول فاشلة للمستخدم {Username}", usernameTextBox.Text);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في عملية تسجيل الدخول للمستخدم {Username}", usernameTextBox.Text);
                ShowError("خطأ في الاتصال بالخادم");
            }
            finally
            {
                SetControlsEnabled(true);
            }
        }

        private void SetControlsEnabled(bool enabled)
        {
            usernameTextBox.Enabled = enabled;
            passwordTextBox.Enabled = enabled;
            loginButton.Enabled = enabled;
            exitButton.Enabled = enabled;
        }

        private void ShowError(string message)
        {
            statusLabel.Text = message;
            statusLabel.ForeColor = Color.Red;
        }
    }

    // Static class to hold current user session
    public static class CurrentUser
    {
        public static POSSystem.Models.User? User { get; set; }
    }
}
