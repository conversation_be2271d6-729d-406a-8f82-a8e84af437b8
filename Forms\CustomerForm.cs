using Microsoft.Extensions.Logging;
using POSSystem.Models;
using POSSystem.Services;
using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace POSSystem.Forms
{
    public partial class CustomerForm : Form
    {
        private readonly ICustomerService _customerService;
        private readonly ILogger<CustomerForm> _logger;

        // Controls
        private Panel topPanel;
        private Panel bottomPanel;
        private Panel centerPanel;
        private DataGridView customersGrid;
        private Button addButton;
        private Button editButton;
        private Button deleteButton;
        private Button refreshButton;
        private TextBox searchTextBox;
        private Label searchLabel;

        // Customer details panel
        private GroupBox customerDetailsGroup;
        private TextBox customerCodeTextBox;
        private TextBox customerNameTextBox;
        private TextBox contactPersonTextBox;
        private TextBox addressTextBox;
        private TextBox phoneTextBox;
        private TextBox mobileTextBox;
        private TextBox emailTextBox;
        private TextBox taxNumberTextBox;
        private NumericUpDown creditLimitNumeric;
        private NumericUpDown paymentTermsNumeric;
        private NumericUpDown openingBalanceNumeric;
        private TextBox notesTextBox;
        private CheckBox isActiveCheckBox;
        private Button saveButton;
        private Button cancelButton;

        private Customer? _currentCustomer;
        private bool _isEditing = false;

        public CustomerForm(ICustomerService customerService, ILogger<CustomerForm> logger)
        {
            _customerService = customerService;
            _logger = logger;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "إدارة العملاء";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateTopPanel();
            CreateBottomPanel();
            CreateCenterPanel();
            CreateCustomerDetailsPanel();

            this.Load += CustomerForm_Load;

            this.ResumeLayout(false);
        }

        private void CreateTopPanel()
        {
            topPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(248, 249, 250)
            };

            // Search controls
            searchLabel = new Label
            {
                Text = "البحث:",
                Location = new Point(1050, 20),
                Size = new Size(50, 20),
                Font = new Font("Tahoma", 10)
            };

            searchTextBox = new TextBox
            {
                Location = new Point(850, 18),
                Size = new Size(200, 25),
                Font = new Font("Tahoma", 10)
            };
            searchTextBox.TextChanged += SearchTextBox_TextChanged;

            // Buttons
            addButton = new Button
            {
                Text = "إضافة",
                Location = new Point(750, 15),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            addButton.Click += AddButton_Click;

            editButton = new Button
            {
                Text = "تعديل",
                Location = new Point(660, 15),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat
            };
            editButton.Click += EditButton_Click;

            deleteButton = new Button
            {
                Text = "حذف",
                Location = new Point(570, 15),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            deleteButton.Click += DeleteButton_Click;

            refreshButton = new Button
            {
                Text = "تحديث",
                Location = new Point(480, 15),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            refreshButton.Click += RefreshButton_Click;

            topPanel.Controls.AddRange(new Control[] { searchLabel, searchTextBox, addButton, editButton, deleteButton, refreshButton });
            this.Controls.Add(topPanel);
        }

        private void CreateBottomPanel()
        {
            bottomPanel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Bottom,
                BackColor = Color.FromArgb(248, 249, 250)
            };

            this.Controls.Add(bottomPanel);
        }

        private void CreateCenterPanel()
        {
            centerPanel = new Panel
            {
                Dock = DockStyle.Fill
            };

            // Create DataGridView
            customersGrid = new DataGridView
            {
                Dock = DockStyle.Left,
                Width = 700,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                Font = new Font("Tahoma", 9)
            };

            customersGrid.SelectionChanged += CustomersGrid_SelectionChanged;

            centerPanel.Controls.Add(customersGrid);
            this.Controls.Add(centerPanel);
        }

        private void CreateCustomerDetailsPanel()
        {
            customerDetailsGroup = new GroupBox
            {
                Text = "بيانات العميل",
                Dock = DockStyle.Fill,
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Padding = new Padding(10)
            };

            // Customer Code
            var customerCodeLabel = new Label { Text = "كود العميل:", Location = new Point(400, 30), Size = new Size(80, 20) };
            customerCodeTextBox = new TextBox { Location = new Point(250, 28), Size = new Size(150, 25), ReadOnly = true };

            // Customer Name
            var customerNameLabel = new Label { Text = "اسم العميل:", Location = new Point(400, 65), Size = new Size(80, 20) };
            customerNameTextBox = new TextBox { Location = new Point(250, 63), Size = new Size(150, 25) };

            // Contact Person
            var contactPersonLabel = new Label { Text = "الشخص المسؤول:", Location = new Point(400, 100), Size = new Size(80, 20) };
            contactPersonTextBox = new TextBox { Location = new Point(250, 98), Size = new Size(150, 25) };

            // Address
            var addressLabel = new Label { Text = "العنوان:", Location = new Point(400, 135), Size = new Size(80, 20) };
            addressTextBox = new TextBox { Location = new Point(250, 133), Size = new Size(150, 25) };

            // Phone
            var phoneLabel = new Label { Text = "الهاتف:", Location = new Point(400, 170), Size = new Size(80, 20) };
            phoneTextBox = new TextBox { Location = new Point(250, 168), Size = new Size(150, 25) };

            // Mobile
            var mobileLabel = new Label { Text = "الجوال:", Location = new Point(400, 205), Size = new Size(80, 20) };
            mobileTextBox = new TextBox { Location = new Point(250, 203), Size = new Size(150, 25) };

            // Email
            var emailLabel = new Label { Text = "البريد الإلكتروني:", Location = new Point(400, 240), Size = new Size(80, 20) };
            emailTextBox = new TextBox { Location = new Point(250, 238), Size = new Size(150, 25) };

            // Tax Number
            var taxNumberLabel = new Label { Text = "الرقم الضريبي:", Location = new Point(400, 275), Size = new Size(80, 20) };
            taxNumberTextBox = new TextBox { Location = new Point(250, 273), Size = new Size(150, 25) };

            // Credit Limit
            var creditLimitLabel = new Label { Text = "حد الائتمان:", Location = new Point(400, 310), Size = new Size(80, 20) };
            creditLimitNumeric = new NumericUpDown { Location = new Point(250, 308), Size = new Size(150, 25), Maximum = *********, DecimalPlaces = 2 };

            // Payment Terms
            var paymentTermsLabel = new Label { Text = "شروط الدفع (أيام):", Location = new Point(400, 345), Size = new Size(80, 20) };
            paymentTermsNumeric = new NumericUpDown { Location = new Point(250, 343), Size = new Size(150, 25), Maximum = 365 };

            // Opening Balance
            var openingBalanceLabel = new Label { Text = "الرصيد الافتتاحي:", Location = new Point(400, 380), Size = new Size(80, 20) };
            openingBalanceNumeric = new NumericUpDown { Location = new Point(250, 378), Size = new Size(150, 25), Maximum = *********, Minimum = -*********, DecimalPlaces = 2 };

            // Notes
            var notesLabel = new Label { Text = "ملاحظات:", Location = new Point(400, 415), Size = new Size(80, 20) };
            notesTextBox = new TextBox { Location = new Point(250, 413), Size = new Size(150, 60), Multiline = true };

            // Is Active
            isActiveCheckBox = new CheckBox { Text = "نشط", Location = new Point(250, 485), Size = new Size(60, 20), Checked = true };

            // Buttons
            saveButton = new Button
            {
                Text = "حفظ",
                Location = new Point(350, 520),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };
            saveButton.Click += SaveButton_Click;

            cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(260, 520),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };
            cancelButton.Click += CancelButton_Click;

            customerDetailsGroup.Controls.AddRange(new Control[]
            {
                customerCodeLabel, customerCodeTextBox,
                customerNameLabel, customerNameTextBox,
                contactPersonLabel, contactPersonTextBox,
                addressLabel, addressTextBox,
                phoneLabel, phoneTextBox,
                mobileLabel, mobileTextBox,
                emailLabel, emailTextBox,
                taxNumberLabel, taxNumberTextBox,
                creditLimitLabel, creditLimitNumeric,
                paymentTermsLabel, paymentTermsNumeric,
                openingBalanceLabel, openingBalanceNumeric,
                notesLabel, notesTextBox,
                isActiveCheckBox,
                saveButton, cancelButton
            });

            centerPanel.Controls.Add(customerDetailsGroup);
        }

        private async void CustomerForm_Load(object sender, EventArgs e)
        {
            try
            {
                await LoadCustomers();
                SetupGridColumns();
                SetEditMode(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل نموذج العملاء");
                MessageBox.Show("خطأ في تحميل البيانات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LoadCustomers()
        {
            try
            {
                var customers = await _customerService.GetAllCustomersAsync();
                customersGrid.DataSource = customers.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل العملاء");
                throw;
            }
        }

        private void SetupGridColumns()
        {
            if (customersGrid.Columns.Count > 0)
            {
                customersGrid.Columns["Id"].Visible = false;
                customersGrid.Columns["CustomerCode"].HeaderText = "الكود";
                customersGrid.Columns["CustomerName"].HeaderText = "الاسم";
                customersGrid.Columns["Phone"].HeaderText = "الهاتف";
                customersGrid.Columns["Mobile"].HeaderText = "الجوال";
                customersGrid.Columns["CurrentBalance"].HeaderText = "الرصيد الحالي";
                customersGrid.Columns["IsActive"].HeaderText = "نشط";
                
                // Hide other columns
                foreach (DataGridViewColumn column in customersGrid.Columns)
                {
                    if (!new[] { "CustomerCode", "CustomerName", "Phone", "Mobile", "CurrentBalance", "IsActive" }.Contains(column.Name))
                    {
                        column.Visible = false;
                    }
                }
            }
        }

        private void CustomersGrid_SelectionChanged(object sender, EventArgs e)
        {
            if (customersGrid.SelectedRows.Count > 0 && !_isEditing)
            {
                var selectedCustomer = (Customer)customersGrid.SelectedRows[0].DataBoundItem;
                DisplayCustomer(selectedCustomer);
            }
        }

        private void DisplayCustomer(Customer customer)
        {
            _currentCustomer = customer;
            customerCodeTextBox.Text = customer.CustomerCode;
            customerNameTextBox.Text = customer.CustomerName;
            contactPersonTextBox.Text = customer.ContactPerson ?? "";
            addressTextBox.Text = customer.Address ?? "";
            phoneTextBox.Text = customer.Phone ?? "";
            mobileTextBox.Text = customer.Mobile ?? "";
            emailTextBox.Text = customer.Email ?? "";
            taxNumberTextBox.Text = customer.TaxNumber ?? "";
            creditLimitNumeric.Value = customer.CreditLimit;
            paymentTermsNumeric.Value = customer.PaymentTerms;
            openingBalanceNumeric.Value = customer.OpeningBalance;
            notesTextBox.Text = customer.Notes ?? "";
            isActiveCheckBox.Checked = customer.IsActive;
        }

        private void ClearCustomerForm()
        {
            _currentCustomer = null;
            customerCodeTextBox.Clear();
            customerNameTextBox.Clear();
            contactPersonTextBox.Clear();
            addressTextBox.Clear();
            phoneTextBox.Clear();
            mobileTextBox.Clear();
            emailTextBox.Clear();
            taxNumberTextBox.Clear();
            creditLimitNumeric.Value = 0;
            paymentTermsNumeric.Value = 0;
            openingBalanceNumeric.Value = 0;
            notesTextBox.Clear();
            isActiveCheckBox.Checked = true;
        }

        private void SetEditMode(bool isEditing)
        {
            _isEditing = isEditing;
            
            // Enable/disable form controls
            customerNameTextBox.ReadOnly = !isEditing;
            contactPersonTextBox.ReadOnly = !isEditing;
            addressTextBox.ReadOnly = !isEditing;
            phoneTextBox.ReadOnly = !isEditing;
            mobileTextBox.ReadOnly = !isEditing;
            emailTextBox.ReadOnly = !isEditing;
            taxNumberTextBox.ReadOnly = !isEditing;
            creditLimitNumeric.Enabled = isEditing;
            paymentTermsNumeric.Enabled = isEditing;
            openingBalanceNumeric.Enabled = isEditing;
            notesTextBox.ReadOnly = !isEditing;
            isActiveCheckBox.Enabled = isEditing;

            // Enable/disable buttons
            saveButton.Enabled = isEditing;
            cancelButton.Enabled = isEditing;
            addButton.Enabled = !isEditing;
            editButton.Enabled = !isEditing && _currentCustomer != null;
            deleteButton.Enabled = !isEditing && _currentCustomer != null;
            customersGrid.Enabled = !isEditing;
        }

        private async void AddButton_Click(object sender, EventArgs e)
        {
            try
            {
                ClearCustomerForm();
                customerCodeTextBox.Text = await _customerService.GenerateCustomerCodeAsync();
                SetEditMode(true);
                customerNameTextBox.Focus();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة عميل جديد");
                MessageBox.Show("خطأ في إضافة عميل جديد", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (_currentCustomer != null)
            {
                SetEditMode(true);
                customerNameTextBox.Focus();
            }
        }

        private async void DeleteButton_Click(object sender, EventArgs e)
        {
            if (_currentCustomer != null)
            {
                var result = MessageBox.Show($"هل تريد حذف العميل '{_currentCustomer.CustomerName}'؟", 
                    "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        await _customerService.DeleteCustomerAsync(_currentCustomer.Id);
                        await LoadCustomers();
                        ClearCustomerForm();
                        MessageBox.Show("تم حذف العميل بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "خطأ في حذف العميل");
                        MessageBox.Show("خطأ في حذف العميل", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private async void RefreshButton_Click(object sender, EventArgs e)
        {
            try
            {
                await LoadCustomers();
                ClearCustomerForm();
                SetEditMode(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث البيانات");
                MessageBox.Show("خطأ في تحديث البيانات", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(customerNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم العميل", "تحقق", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    customerNameTextBox.Focus();
                    return;
                }

                var customer = new Customer
                {
                    Id = _currentCustomer?.Id ?? 0,
                    CustomerCode = customerCodeTextBox.Text,
                    CustomerName = customerNameTextBox.Text,
                    ContactPerson = contactPersonTextBox.Text,
                    Address = addressTextBox.Text,
                    Phone = phoneTextBox.Text,
                    Mobile = mobileTextBox.Text,
                    Email = emailTextBox.Text,
                    TaxNumber = taxNumberTextBox.Text,
                    CreditLimit = creditLimitNumeric.Value,
                    PaymentTerms = (int)paymentTermsNumeric.Value,
                    OpeningBalance = openingBalanceNumeric.Value,
                    Notes = notesTextBox.Text,
                    IsActive = isActiveCheckBox.Checked
                };

                if (_currentCustomer == null)
                {
                    // Add new customer
                    await _customerService.AddCustomerAsync(customer);
                    MessageBox.Show("تم إضافة العميل بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // Update existing customer
                    await _customerService.UpdateCustomerAsync(customer);
                    MessageBox.Show("تم تحديث العميل بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                await LoadCustomers();
                SetEditMode(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حفظ العميل");
                MessageBox.Show("خطأ في حفظ العميل: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            if (_currentCustomer != null)
            {
                DisplayCustomer(_currentCustomer);
            }
            else
            {
                ClearCustomerForm();
            }
            SetEditMode(false);
        }

        private async void SearchTextBox_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTextBox.Text))
                {
                    await LoadCustomers();
                }
                else
                {
                    var customers = await _customerService.SearchCustomersAsync(searchTextBox.Text);
                    customersGrid.DataSource = customers.ToList();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث");
            }
        }
    }
}
