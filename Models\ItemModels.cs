using System;
using System.ComponentModel.DataAnnotations;

namespace POSSystem.Models
{
    public class Item : BaseEntity
    {
        [Required(ErrorMessage = "كود الصنف مطلوب")]
        [StringLength(50, ErrorMessage = "كود الصنف لا يمكن أن يزيد عن 50 حرف")]
        public string ItemCode { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم الصنف مطلوب")]
        [StringLength(200, ErrorMessage = "اسم الصنف لا يمكن أن يزيد عن 200 حرف")]
        public string ItemName { get; set; } = string.Empty;

        [StringLength(200)]
        public string? ItemNameEn { get; set; }

        public int? CategoryId { get; set; }

        [Required(ErrorMessage = "الوحدة الأساسية مطلوبة")]
        public int BaseUnitId { get; set; }

        [StringLength(50)]
        public string? Barcode { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "الحد الأدنى للمخزون يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal MinStockLevel { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "الحد الأقصى للمخزون يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal MaxStockLevel { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "نقطة إعادة الطلب يجب أن تكون أكبر من أو يساوي صفر")]
        public decimal ReorderLevel { get; set; }

        public bool HasExpiry { get; set; }
        public bool IsSerial { get; set; }
    }

    public class ItemCategory : BaseEntity
    {
        [Required(ErrorMessage = "كود المجموعة مطلوب")]
        [StringLength(20, ErrorMessage = "كود المجموعة لا يمكن أن يزيد عن 20 حرف")]
        public string CategoryCode { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم المجموعة مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المجموعة لا يمكن أن يزيد عن 100 حرف")]
        public string CategoryName { get; set; } = string.Empty;

        public int? ParentCategoryId { get; set; }
    }

    public class Unit : BaseEntity
    {
        [Required(ErrorMessage = "كود الوحدة مطلوب")]
        [StringLength(10, ErrorMessage = "كود الوحدة لا يمكن أن يزيد عن 10 أحرف")]
        public string UnitCode { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم الوحدة مطلوب")]
        [StringLength(50, ErrorMessage = "اسم الوحدة لا يمكن أن يزيد عن 50 حرف")]
        public string UnitName { get; set; } = string.Empty;
    }

    public class ItemUnit
    {
        public int ItemId { get; set; }
        public int UnitId { get; set; }

        [Range(0.001, double.MaxValue, ErrorMessage = "معامل التحويل يجب أن يكون أكبر من صفر")]
        public decimal ConversionFactor { get; set; } = 1;

        [Range(0, double.MaxValue, ErrorMessage = "سعر الشراء يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal PurchasePrice { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "سعر البيع يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal SalePrice { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "سعر الجملة يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal WholesalePrice { get; set; }

        public bool IsDefault { get; set; }
    }

    public class ItemStock
    {
        public int ItemId { get; set; }
        public int WarehouseId { get; set; }
        public int UnitId { get; set; }
        public decimal QuantityOnHand { get; set; }
        public decimal QuantityReserved { get; set; }
        public decimal QuantityAvailable { get; set; }
        public decimal AverageCost { get; set; }
        public decimal LastCost { get; set; }
        public DateTime LastUpdateDate { get; set; }
    }

    public class Warehouse : BaseEntity
    {
        [Required(ErrorMessage = "كود المخزن مطلوب")]
        [StringLength(20, ErrorMessage = "كود المخزن لا يمكن أن يزيد عن 20 حرف")]
        public string WarehouseCode { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم المخزن مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المخزن لا يمكن أن يزيد عن 100 حرف")]
        public string WarehouseName { get; set; } = string.Empty;

        [StringLength(200)]
        public string? Location { get; set; }

        [StringLength(100)]
        public string? Manager { get; set; }

        [StringLength(20)]
        public string? Phone { get; set; }
    }

    public class Treasury : BaseEntity
    {
        [Required(ErrorMessage = "كود الخزينة مطلوب")]
        [StringLength(20, ErrorMessage = "كود الخزينة لا يمكن أن يزيد عن 20 حرف")]
        public string TreasuryCode { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم الخزينة مطلوب")]
        [StringLength(100, ErrorMessage = "اسم الخزينة لا يمكن أن يزيد عن 100 حرف")]
        public string TreasuryName { get; set; } = string.Empty;

        public decimal OpeningBalance { get; set; }
        public decimal CurrentBalance { get; set; }
    }

    public class User : BaseEntity
    {
        [Required(ErrorMessage = "اسم المستخدم مطلوب")]
        [StringLength(50, ErrorMessage = "اسم المستخدم لا يمكن أن يزيد عن 50 حرف")]
        public string Username { get; set; } = string.Empty;

        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        [StringLength(255)]
        public string Password { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الكامل مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم الكامل لا يمكن أن يزيد عن 100 حرف")]
        public string FullName { get; set; } = string.Empty;

        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [StringLength(100)]
        public string? Email { get; set; }

        [StringLength(20)]
        public string? Phone { get; set; }

        public DateTime? LastLoginDate { get; set; }
    }

    public class UserPermission
    {
        public int UserId { get; set; }
        public string PermissionCode { get; set; } = string.Empty;
        public string PermissionName { get; set; } = string.Empty;
        public bool HasPermission { get; set; }
    }

    public class StockMovement : BaseEntity
    {
        public int ItemId { get; set; }
        public int WarehouseId { get; set; }
        public int UnitId { get; set; }
        public string MovementType { get; set; } = string.Empty; // In, Out, Transfer, Adjustment
        public decimal Quantity { get; set; }
        public decimal UnitCost { get; set; }
        public string? ReferenceType { get; set; } // SalesInvoice, PurchaseInvoice, etc.
        public int? ReferenceId { get; set; }
        public string? Notes { get; set; }
        public DateTime MovementDate { get; set; }
    }
}
