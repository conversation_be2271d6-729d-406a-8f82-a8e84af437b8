-- إعداد سريع لقاعدة البيانات
-- نظام إدارة المبيعات والمشتريات والمخازن

-- إنشاء قاعدة البيانات
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'POSSystem')
BEGIN
    CREATE DATABASE POSSystem;
END
GO

USE POSSystem;
GO

-- إنشاء الجداول الأساسية

-- جدول الوحدات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Units' AND xtype='U')
BEGIN
    CREATE TABLE Units (
        UnitID int IDENTITY(1,1) PRIMARY KEY,
        UnitCode nvarchar(10) NOT NULL UNIQUE,
        UnitName nvarchar(50) NOT NULL,
        IsActive bit NOT NULL DEFAULT 1
    );

    -- إدراج وحدات افتراضية
    INSERT INTO Units (UnitCode, UnitName) VALUES
    ('PCS', 'قطعة'),
    ('KG', 'كيلوجرام'),
    ('LTR', 'لتر'),
    ('BOX', 'صندوق'),
    ('CTN', 'كرتونة');
END
GO

-- جدول مجموعات الأصناف
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ItemCategories' AND xtype='U')
BEGIN
    CREATE TABLE ItemCategories (
        CategoryID int IDENTITY(1,1) PRIMARY KEY,
        CategoryCode nvarchar(20) NOT NULL UNIQUE,
        CategoryName nvarchar(100) NOT NULL,
        ParentCategoryID int NULL,
        IsActive bit NOT NULL DEFAULT 1,
        FOREIGN KEY (ParentCategoryID) REFERENCES ItemCategories(CategoryID)
    );

    -- إدراج مجموعات افتراضية
    INSERT INTO ItemCategories (CategoryCode, CategoryName) VALUES
    ('FOOD', 'مواد غذائية'),
    ('ELEC', 'أجهزة كهربائية'),
    ('CLOTH', 'ملابس'),
    ('BOOKS', 'كتب ومكتبة'),
    ('TOOLS', 'أدوات');
END
GO

-- جدول العملاء
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Customers' AND xtype='U')
BEGIN
    CREATE TABLE Customers (
        CustomerID int IDENTITY(1,1) PRIMARY KEY,
        CustomerCode nvarchar(20) NOT NULL UNIQUE,
        CustomerName nvarchar(100) NOT NULL,
        ContactPerson nvarchar(100) NULL,
        Address nvarchar(200) NULL,
        Phone nvarchar(20) NULL,
        Mobile nvarchar(20) NULL,
        Email nvarchar(100) NULL,
        TaxNumber nvarchar(50) NULL,
        CreditLimit decimal(18,2) NOT NULL DEFAULT 0,
        PaymentTerms int NOT NULL DEFAULT 0,
        OpeningBalance decimal(18,2) NOT NULL DEFAULT 0,
        CurrentBalance decimal(18,2) NOT NULL DEFAULT 0,
        IsActive bit NOT NULL DEFAULT 1,
        CreatedDate datetime NOT NULL DEFAULT GETDATE(),
        Notes nvarchar(500) NULL
    );

    -- إدراج عميل افتراضي
    INSERT INTO Customers (CustomerCode, CustomerName, CurrentBalance) VALUES
    ('C000001', 'عميل نقدي', 0);
END
GO

-- جدول الموردين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Suppliers' AND xtype='U')
BEGIN
    CREATE TABLE Suppliers (
        SupplierID int IDENTITY(1,1) PRIMARY KEY,
        SupplierCode nvarchar(20) NOT NULL UNIQUE,
        SupplierName nvarchar(100) NOT NULL,
        ContactPerson nvarchar(100) NULL,
        Address nvarchar(200) NULL,
        Phone nvarchar(20) NULL,
        Mobile nvarchar(20) NULL,
        Email nvarchar(100) NULL,
        TaxNumber nvarchar(50) NULL,
        PaymentTerms int NOT NULL DEFAULT 0,
        OpeningBalance decimal(18,2) NOT NULL DEFAULT 0,
        CurrentBalance decimal(18,2) NOT NULL DEFAULT 0,
        IsActive bit NOT NULL DEFAULT 1,
        CreatedDate datetime NOT NULL DEFAULT GETDATE(),
        Notes nvarchar(500) NULL
    );

    -- إدراج مورد افتراضي
    INSERT INTO Suppliers (SupplierCode, SupplierName, CurrentBalance) VALUES
    ('S000001', 'مورد عام', 0);
END
GO

-- جدول المخازن
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Warehouses' AND xtype='U')
BEGIN
    CREATE TABLE Warehouses (
        WarehouseID int IDENTITY(1,1) PRIMARY KEY,
        WarehouseCode nvarchar(20) NOT NULL UNIQUE,
        WarehouseName nvarchar(100) NOT NULL,
        Location nvarchar(200) NULL,
        Manager nvarchar(100) NULL,
        Phone nvarchar(20) NULL,
        IsActive bit NOT NULL DEFAULT 1
    );

    -- إدراج مخزن افتراضي
    INSERT INTO Warehouses (WarehouseCode, WarehouseName) VALUES
    ('WH001', 'المخزن الرئيسي');
END
GO

-- جدول الخزائن
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Treasuries' AND xtype='U')
BEGIN
    CREATE TABLE Treasuries (
        TreasuryID int IDENTITY(1,1) PRIMARY KEY,
        TreasuryCode nvarchar(20) NOT NULL UNIQUE,
        TreasuryName nvarchar(100) NOT NULL,
        OpeningBalance decimal(18,2) NOT NULL DEFAULT 0,
        CurrentBalance decimal(18,2) NOT NULL DEFAULT 0,
        IsActive bit NOT NULL DEFAULT 1
    );

    -- إدراج خزينة افتراضية
    INSERT INTO Treasuries (TreasuryCode, TreasuryName, CurrentBalance) VALUES
    ('TR001', 'الخزينة الرئيسية', 0);
END
GO

-- جدول الأصناف
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Items' AND xtype='U')
BEGIN
    CREATE TABLE Items (
        ItemID int IDENTITY(1,1) PRIMARY KEY,
        ItemCode nvarchar(50) NOT NULL UNIQUE,
        ItemName nvarchar(200) NOT NULL,
        ItemNameEn nvarchar(200) NULL,
        CategoryID int NULL,
        BaseUnitID int NOT NULL,
        Barcode nvarchar(50) NULL UNIQUE,
        Description nvarchar(500) NULL,
        MinStockLevel decimal(18,3) NOT NULL DEFAULT 0,
        MaxStockLevel decimal(18,3) NOT NULL DEFAULT 0,
        ReorderLevel decimal(18,3) NOT NULL DEFAULT 0,
        HasExpiry bit NOT NULL DEFAULT 0,
        IsSerial bit NOT NULL DEFAULT 0,
        IsActive bit NOT NULL DEFAULT 1,
        CreatedDate datetime NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY (CategoryID) REFERENCES ItemCategories(CategoryID),
        FOREIGN KEY (BaseUnitID) REFERENCES Units(UnitID)
    );
END
GO

-- جدول وحدات الأصناف
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ItemUnits' AND xtype='U')
BEGIN
    CREATE TABLE ItemUnits (
        ItemID int NOT NULL,
        UnitID int NOT NULL,
        ConversionFactor decimal(18,3) NOT NULL DEFAULT 1,
        PurchasePrice decimal(18,2) NOT NULL DEFAULT 0,
        SalePrice decimal(18,2) NOT NULL DEFAULT 0,
        WholesalePrice decimal(18,2) NOT NULL DEFAULT 0,
        IsDefault bit NOT NULL DEFAULT 0,
        PRIMARY KEY (ItemID, UnitID),
        FOREIGN KEY (ItemID) REFERENCES Items(ItemID),
        FOREIGN KEY (UnitID) REFERENCES Units(UnitID)
    );
END
GO

-- جدول مخزون الأصناف
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ItemStock' AND xtype='U')
BEGIN
    CREATE TABLE ItemStock (
        ItemID int NOT NULL,
        WarehouseID int NOT NULL,
        UnitID int NOT NULL,
        QuantityOnHand decimal(18,3) NOT NULL DEFAULT 0,
        QuantityReserved decimal(18,3) NOT NULL DEFAULT 0,
        QuantityAvailable decimal(18,3) NOT NULL DEFAULT 0,
        AverageCost decimal(18,2) NOT NULL DEFAULT 0,
        LastCost decimal(18,2) NOT NULL DEFAULT 0,
        LastUpdateDate datetime NOT NULL DEFAULT GETDATE(),
        PRIMARY KEY (ItemID, WarehouseID, UnitID),
        FOREIGN KEY (ItemID) REFERENCES Items(ItemID),
        FOREIGN KEY (WarehouseID) REFERENCES Warehouses(WarehouseID),
        FOREIGN KEY (UnitID) REFERENCES Units(UnitID)
    );
END
GO

-- جدول فواتير البيع
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SalesInvoices' AND xtype='U')
BEGIN
    CREATE TABLE SalesInvoices (
        SalesInvoiceID int IDENTITY(1,1) PRIMARY KEY,
        InvoiceNumber nvarchar(50) NOT NULL UNIQUE,
        InvoiceDate datetime NOT NULL,
        CustomerID int NOT NULL,
        SalesRepID int NULL,
        WarehouseID int NOT NULL,
        QuotationID int NULL,
        SubTotal decimal(18,2) NOT NULL DEFAULT 0,
        DiscountAmount decimal(18,2) NOT NULL DEFAULT 0,
        DiscountPercent decimal(5,2) NOT NULL DEFAULT 0,
        TaxAmount decimal(18,2) NOT NULL DEFAULT 0,
        TaxPercent decimal(5,2) NOT NULL DEFAULT 0,
        TotalAmount decimal(18,2) NOT NULL DEFAULT 0,
        PaidAmount decimal(18,2) NOT NULL DEFAULT 0,
        RemainingAmount decimal(18,2) NOT NULL DEFAULT 0,
        PaymentStatus nvarchar(20) NOT NULL DEFAULT 'Pending',
        Status nvarchar(20) NOT NULL DEFAULT 'Draft',
        DueDate datetime NULL,
        Notes nvarchar(500) NULL,
        CreatedBy int NULL,
        CreatedDate datetime NOT NULL DEFAULT GETDATE(),
        PostedBy int NULL,
        PostedDate datetime NULL,
        FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID),
        FOREIGN KEY (WarehouseID) REFERENCES Warehouses(WarehouseID)
    );
END
GO

-- جدول تفاصيل فواتير البيع
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SalesInvoiceDetails' AND xtype='U')
BEGIN
    CREATE TABLE SalesInvoiceDetails (
        SalesInvoiceDetailID int IDENTITY(1,1) PRIMARY KEY,
        SalesInvoiceID int NOT NULL,
        ItemID int NOT NULL,
        UnitID int NOT NULL,
        Quantity decimal(18,3) NOT NULL,
        UnitPrice decimal(18,2) NOT NULL,
        UnitCost decimal(18,2) NOT NULL DEFAULT 0,
        DiscountAmount decimal(18,2) NOT NULL DEFAULT 0,
        DiscountPercent decimal(5,2) NOT NULL DEFAULT 0,
        TaxAmount decimal(18,2) NOT NULL DEFAULT 0,
        TaxPercent decimal(5,2) NOT NULL DEFAULT 0,
        LineTotal decimal(18,2) NOT NULL,
        ExpiryDate datetime NULL,
        BatchNumber nvarchar(50) NULL,
        SerialNumber nvarchar(50) NULL,
        Notes nvarchar(200) NULL,
        FOREIGN KEY (SalesInvoiceID) REFERENCES SalesInvoices(SalesInvoiceID),
        FOREIGN KEY (ItemID) REFERENCES Items(ItemID),
        FOREIGN KEY (UnitID) REFERENCES Units(UnitID)
    );
END
GO

-- جدول المستخدمين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    CREATE TABLE Users (
        UserID int IDENTITY(1,1) PRIMARY KEY,
        Username nvarchar(50) NOT NULL UNIQUE,
        Password nvarchar(255) NOT NULL,
        FullName nvarchar(100) NOT NULL,
        Email nvarchar(100) NULL,
        Phone nvarchar(20) NULL,
        IsActive bit NOT NULL DEFAULT 1,
        CreatedDate datetime NOT NULL DEFAULT GETDATE(),
        LastLoginDate datetime NULL
    );

    -- إدراج مستخدم افتراضي (admin/admin123)
    INSERT INTO Users (Username, Password, FullName) VALUES
    ('admin', 'admin123', 'مدير النظام');
END
GO

PRINT 'تم إنشاء قاعدة البيانات بنجاح!';
PRINT 'بيانات الدخول الافتراضية:';
PRINT 'اسم المستخدم: admin';
PRINT 'كلمة المرور: admin123';