using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using POSSystem.Data;
using POSSystem.Data.Interfaces;
using POSSystem.Data.Repositories;
using POSSystem.Services;
using POSSystem.Forms;
using Serilog;
using System;
using System.IO;
using System.Windows.Forms;

namespace POSSystem
{
    internal static class Program
    {
        public static IServiceProvider ServiceProvider { get; private set; } = null!;
        public static IConfiguration Configuration { get; private set; } = null!;

        /// <summary>
        ///  The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // إعداد التكوين
            var builder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

            Configuration = builder.Build();

            // إعداد Serilog
            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(Configuration)
                .CreateLogger();

            try
            {
                Log.Information("بدء تشغيل نظام إدارة المبيعات والمشتريات");

                // إعداد خدمات DI
                var services = new ServiceCollection();
                ConfigureServices(services);
                ServiceProvider = services.BuildServiceProvider();

                // إعداد Windows Forms
                Application.SetHighDpiMode(HighDpiMode.SystemAware);
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // تشغيل النظام
                var loginForm = ServiceProvider.GetRequiredService<LoginForm>();
                Application.Run(loginForm);
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "خطأ في بدء تشغيل النظام");
                MessageBox.Show($"خطأ في بدء تشغيل النظام: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                Log.CloseAndFlush();
                ServiceProvider?.Dispose();
            }
        }

        private static void ConfigureServices(IServiceCollection services)
        {
            // إضافة التكوين
            services.AddSingleton(Configuration);

            // إضافة Logging
            services.AddLogging(builder =>
            {
                builder.ClearProviders();
                builder.AddSerilog();
            });

            // إضافة خدمات البيانات
            services.AddSingleton<DatabaseManager>();

            // إضافة Repositories
            services.AddScoped<ICustomerRepository, CustomerRepository>();
            services.AddScoped<ISupplierRepository, SupplierRepository>();
            services.AddScoped<IItemRepository, ItemRepository>();
            services.AddScoped<IUnitRepository, UnitRepository>();
            services.AddScoped<IItemCategoryRepository, ItemCategoryRepository>();
            services.AddScoped<IWarehouseRepository, WarehouseRepository>();
            services.AddScoped<ISalesInvoiceRepository, SalesInvoiceRepository>();
            services.AddScoped<IUserRepository, UserRepository>();

            // إضافة Services
            services.AddScoped<ICustomerService, CustomerService>();
            services.AddScoped<ISupplierService, SupplierService>();
            services.AddScoped<IItemService, ItemService>();
            services.AddScoped<ISalesService, SalesService>();
            services.AddScoped<IUserService, UserService>();

            // إضافة النماذج
            services.AddTransient<LoginForm>();
            services.AddTransient<MainForm>();
            services.AddTransient<CustomerForm>();
            services.AddTransient<SupplierForm>();
            services.AddTransient<ItemForm>();
            services.AddTransient<SalesInvoiceForm>();
        }
    }
}
