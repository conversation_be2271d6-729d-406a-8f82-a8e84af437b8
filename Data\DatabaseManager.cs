using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;

namespace POSSystem.Data
{
    public class DatabaseManager : IDisposable
    {
        private readonly string _connectionString;
        private readonly ILogger<DatabaseManager> _logger;
        private SqlConnection? _connection;

        public DatabaseManager(IConfiguration configuration, ILogger<DatabaseManager> logger)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") 
                ?? throw new ArgumentNullException(nameof(configuration), "Connection string not found");
            _logger = logger;
        }

        public async Task<SqlConnection> GetConnectionAsync()
        {
            if (_connection == null || _connection.State != ConnectionState.Open)
            {
                _connection = new SqlConnection(_connectionString);
                await _connection.OpenAsync();
                _logger.LogDebug("تم فتح اتصال جديد بقاعدة البيانات");
            }
            return _connection;
        }

        public SqlConnection GetConnection()
        {
            if (_connection == null || _connection.State != ConnectionState.Open)
            {
                _connection = new SqlConnection(_connectionString);
                _connection.Open();
                _logger.LogDebug("تم فتح اتصال جديد بقاعدة البيانات");
            }
            return _connection;
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                _logger.LogInformation("تم اختبار الاتصال بقاعدة البيانات بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "فشل في الاتصال بقاعدة البيانات");
                return false;
            }
        }

        public async Task<DataTable> ExecuteQueryAsync(string query, params SqlParameter[] parameters)
        {
            try
            {
                var connection = await GetConnectionAsync();
                using var command = new SqlCommand(query, connection);
                
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                using var adapter = new SqlDataAdapter(command);
                var dataTable = new DataTable();
                adapter.Fill(dataTable);

                _logger.LogDebug("تم تنفيذ الاستعلام بنجاح: {Query}", query);
                return dataTable;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تنفيذ الاستعلام: {Query}", query);
                throw;
            }
        }

        public DataTable ExecuteQuery(string query, params SqlParameter[] parameters)
        {
            try
            {
                var connection = GetConnection();
                using var command = new SqlCommand(query, connection);
                
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                using var adapter = new SqlDataAdapter(command);
                var dataTable = new DataTable();
                adapter.Fill(dataTable);

                _logger.LogDebug("تم تنفيذ الاستعلام بنجاح: {Query}", query);
                return dataTable;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تنفيذ الاستعلام: {Query}", query);
                throw;
            }
        }

        public async Task<int> ExecuteNonQueryAsync(string query, params SqlParameter[] parameters)
        {
            try
            {
                var connection = await GetConnectionAsync();
                using var command = new SqlCommand(query, connection);
                
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                var result = await command.ExecuteNonQueryAsync();
                _logger.LogDebug("تم تنفيذ الأمر بنجاح: {Query}, تأثر {RowsAffected} صف", query, result);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تنفيذ الأمر: {Query}", query);
                throw;
            }
        }

        public int ExecuteNonQuery(string query, params SqlParameter[] parameters)
        {
            try
            {
                var connection = GetConnection();
                using var command = new SqlCommand(query, connection);
                
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                var result = command.ExecuteNonQuery();
                _logger.LogDebug("تم تنفيذ الأمر بنجاح: {Query}, تأثر {RowsAffected} صف", query, result);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تنفيذ الأمر: {Query}", query);
                throw;
            }
        }

        public async Task<object?> ExecuteScalarAsync(string query, params SqlParameter[] parameters)
        {
            try
            {
                var connection = await GetConnectionAsync();
                using var command = new SqlCommand(query, connection);
                
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                var result = await command.ExecuteScalarAsync();
                _logger.LogDebug("تم تنفيذ الاستعلام المفرد بنجاح: {Query}", query);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تنفيذ الاستعلام المفرد: {Query}", query);
                throw;
            }
        }

        public object? ExecuteScalar(string query, params SqlParameter[] parameters)
        {
            try
            {
                var connection = GetConnection();
                using var command = new SqlCommand(query, connection);
                
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                var result = command.ExecuteScalar();
                _logger.LogDebug("تم تنفيذ الاستعلام المفرد بنجاح: {Query}", query);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تنفيذ الاستعلام المفرد: {Query}", query);
                throw;
            }
        }

        public async Task<bool> CheckDatabaseExistsAsync()
        {
            try
            {
                var query = "SELECT COUNT(*) FROM sys.databases WHERE name = 'POSSystem'";
                var result = await ExecuteScalarAsync(query);
                return Convert.ToInt32(result) > 0;
            }
            catch
            {
                return false;
            }
        }

        public async Task CreateDatabaseAsync()
        {
            try
            {
                var scriptPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Database", "POSDatabase.sql");
                if (File.Exists(scriptPath))
                {
                    var script = await File.ReadAllTextAsync(scriptPath);
                    await ExecuteNonQueryAsync(script);
                    _logger.LogInformation("تم إنشاء قاعدة البيانات بنجاح");
                }
                else
                {
                    _logger.LogError("لم يتم العثور على ملف إنشاء قاعدة البيانات");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء قاعدة البيانات");
                throw;
            }
        }

        public void Dispose()
        {
            _connection?.Close();
            _connection?.Dispose();
            _logger.LogDebug("تم إغلاق اتصال قاعدة البيانات");
        }
    }
}
