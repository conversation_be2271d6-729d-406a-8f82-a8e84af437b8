using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace POSSystem.Models
{
    public class Quotation : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string QuotationNumber { get; set; } = string.Empty;

        public DateTime QuotationDate { get; set; } = DateTime.Today;

        public int CustomerId { get; set; }
        public Customer Customer { get; set; } = null!;

        public int? SalesRepId { get; set; }
        public SalesRep? SalesRep { get; set; }

        public int WarehouseId { get; set; }
        public Warehouse Warehouse { get; set; } = null!;

        public decimal SubTotal { get; set; } = 0;
        public decimal DiscountAmount { get; set; } = 0;
        public decimal DiscountPercent { get; set; } = 0;
        public decimal TaxAmount { get; set; } = 0;
        public decimal TaxPercent { get; set; } = 0;
        public decimal TotalAmount { get; set; } = 0;

        public DateTime? ValidUntil { get; set; }

        [StringLength(20)]
        public string Status { get; set; } = "Draft"; // Draft, Sent, Accepted, Rejected, Expired

        [StringLength(500)]
        public string? Notes { get; set; }

        public List<QuotationDetail> QuotationDetails { get; set; } = new();
    }

    public class QuotationDetail : BaseEntity
    {
        public int QuotationId { get; set; }
        public Quotation Quotation { get; set; } = null!;

        public int ItemId { get; set; }
        public Item Item { get; set; } = null!;

        public int UnitId { get; set; }
        public Unit Unit { get; set; } = null!;

        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal DiscountAmount { get; set; } = 0;
        public decimal DiscountPercent { get; set; } = 0;
        public decimal TaxAmount { get; set; } = 0;
        public decimal TaxPercent { get; set; } = 0;
        public decimal LineTotal { get; set; }

        [StringLength(200)]
        public string? Notes { get; set; }
    }

    public class SalesInvoice : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string InvoiceNumber { get; set; } = string.Empty;

        public DateTime InvoiceDate { get; set; } = DateTime.Today;

        public int CustomerId { get; set; }
        public Customer Customer { get; set; } = null!;

        public int? SalesRepId { get; set; }
        public SalesRep? SalesRep { get; set; }

        public int WarehouseId { get; set; }
        public Warehouse Warehouse { get; set; } = null!;

        public int? QuotationId { get; set; }
        public Quotation? Quotation { get; set; }

        public decimal SubTotal { get; set; } = 0;
        public decimal DiscountAmount { get; set; } = 0;
        public decimal DiscountPercent { get; set; } = 0;
        public decimal TaxAmount { get; set; } = 0;
        public decimal TaxPercent { get; set; } = 0;
        public decimal TotalAmount { get; set; } = 0;
        public decimal PaidAmount { get; set; } = 0;
        public decimal RemainingAmount { get; set; } = 0;

        [StringLength(20)]
        public string PaymentStatus { get; set; } = "Pending"; // Pending, Partial, Paid

        [StringLength(20)]
        public string Status { get; set; } = "Draft"; // Draft, Posted, Cancelled

        public DateTime? DueDate { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        public int? PostedBy { get; set; }
        public DateTime? PostedDate { get; set; }

        public List<SalesInvoiceDetail> SalesInvoiceDetails { get; set; } = new();
        public List<SalesReturn> SalesReturns { get; set; } = new();
    }

    public class SalesInvoiceDetail : BaseEntity
    {
        public int SalesInvoiceId { get; set; }
        public SalesInvoice SalesInvoice { get; set; } = null!;

        public int ItemId { get; set; }
        public Item Item { get; set; } = null!;

        public int UnitId { get; set; }
        public Unit Unit { get; set; } = null!;

        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal UnitCost { get; set; } = 0;
        public decimal DiscountAmount { get; set; } = 0;
        public decimal DiscountPercent { get; set; } = 0;
        public decimal TaxAmount { get; set; } = 0;
        public decimal TaxPercent { get; set; } = 0;
        public decimal LineTotal { get; set; }

        public DateTime? ExpiryDate { get; set; }

        [StringLength(50)]
        public string? BatchNumber { get; set; }

        [StringLength(50)]
        public string? SerialNumber { get; set; }

        [StringLength(200)]
        public string? Notes { get; set; }

        public List<SalesReturnDetail> SalesReturnDetails { get; set; } = new();
    }

    public class SalesReturn : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string ReturnNumber { get; set; } = string.Empty;

        public DateTime ReturnDate { get; set; } = DateTime.Today;

        public int SalesInvoiceId { get; set; }
        public SalesInvoice SalesInvoice { get; set; } = null!;

        public int CustomerId { get; set; }
        public Customer Customer { get; set; } = null!;

        public int WarehouseId { get; set; }
        public Warehouse Warehouse { get; set; } = null!;

        public decimal SubTotal { get; set; } = 0;
        public decimal DiscountAmount { get; set; } = 0;
        public decimal TaxAmount { get; set; } = 0;
        public decimal TotalAmount { get; set; } = 0;
        public decimal RefundAmount { get; set; } = 0;

        [StringLength(20)]
        public string Status { get; set; } = "Draft"; // Draft, Posted, Cancelled

        [StringLength(200)]
        public string? Reason { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        public int? PostedBy { get; set; }
        public DateTime? PostedDate { get; set; }

        public List<SalesReturnDetail> SalesReturnDetails { get; set; } = new();
    }

    public class SalesReturnDetail : BaseEntity
    {
        public int SalesReturnId { get; set; }
        public SalesReturn SalesReturn { get; set; } = null!;

        public int SalesInvoiceDetailId { get; set; }
        public SalesInvoiceDetail SalesInvoiceDetail { get; set; } = null!;

        public int ItemId { get; set; }
        public Item Item { get; set; } = null!;

        public int UnitId { get; set; }
        public Unit Unit { get; set; } = null!;

        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal DiscountAmount { get; set; } = 0;
        public decimal TaxAmount { get; set; } = 0;
        public decimal LineTotal { get; set; }

        public DateTime? ExpiryDate { get; set; }

        [StringLength(50)]
        public string? BatchNumber { get; set; }

        [StringLength(50)]
        public string? SerialNumber { get; set; }

        [StringLength(200)]
        public string? Notes { get; set; }
    }
}
