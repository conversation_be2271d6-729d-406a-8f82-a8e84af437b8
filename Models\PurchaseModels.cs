using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace POSSystem.Models
{
    public class PurchaseOrder : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string OrderNumber { get; set; } = string.Empty;

        public DateTime OrderDate { get; set; } = DateTime.Today;

        public int SupplierId { get; set; }
        public Supplier Supplier { get; set; } = null!;

        public int WarehouseId { get; set; }
        public Warehouse Warehouse { get; set; } = null!;

        public DateTime? ExpectedDate { get; set; }

        public decimal SubTotal { get; set; } = 0;
        public decimal DiscountAmount { get; set; } = 0;
        public decimal DiscountPercent { get; set; } = 0;
        public decimal TaxAmount { get; set; } = 0;
        public decimal TaxPercent { get; set; } = 0;
        public decimal TotalAmount { get; set; } = 0;

        [StringLength(20)]
        public string Status { get; set; } = "Draft"; // Draft, Sent, Partial, Received, Cancelled

        [StringLength(500)]
        public string? Notes { get; set; }

        public List<PurchaseOrderDetail> PurchaseOrderDetails { get; set; } = new();
        public List<PurchaseInvoice> PurchaseInvoices { get; set; } = new();
    }

    public class PurchaseOrderDetail : BaseEntity
    {
        public int PurchaseOrderId { get; set; }
        public PurchaseOrder PurchaseOrder { get; set; } = null!;

        public int ItemId { get; set; }
        public Item Item { get; set; } = null!;

        public int UnitId { get; set; }
        public Unit Unit { get; set; } = null!;

        public decimal Quantity { get; set; }
        public decimal ReceivedQuantity { get; set; } = 0;
        public decimal UnitPrice { get; set; }
        public decimal DiscountAmount { get; set; } = 0;
        public decimal DiscountPercent { get; set; } = 0;
        public decimal TaxAmount { get; set; } = 0;
        public decimal TaxPercent { get; set; } = 0;
        public decimal LineTotal { get; set; }

        [StringLength(200)]
        public string? Notes { get; set; }
    }

    public class PurchaseInvoice : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string InvoiceNumber { get; set; } = string.Empty;

        [StringLength(50)]
        public string? SupplierInvoiceNumber { get; set; }

        public DateTime InvoiceDate { get; set; } = DateTime.Today;

        public int SupplierId { get; set; }
        public Supplier Supplier { get; set; } = null!;

        public int WarehouseId { get; set; }
        public Warehouse Warehouse { get; set; } = null!;

        public int? PurchaseOrderId { get; set; }
        public PurchaseOrder? PurchaseOrder { get; set; }

        public decimal SubTotal { get; set; } = 0;
        public decimal DiscountAmount { get; set; } = 0;
        public decimal DiscountPercent { get; set; } = 0;
        public decimal TaxAmount { get; set; } = 0;
        public decimal TaxPercent { get; set; } = 0;
        public decimal TotalAmount { get; set; } = 0;
        public decimal PaidAmount { get; set; } = 0;
        public decimal RemainingAmount { get; set; } = 0;

        [StringLength(20)]
        public string PaymentStatus { get; set; } = "Pending"; // Pending, Partial, Paid

        [StringLength(20)]
        public string Status { get; set; } = "Draft"; // Draft, Posted, Cancelled

        public DateTime? DueDate { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        public int? PostedBy { get; set; }
        public DateTime? PostedDate { get; set; }

        public List<PurchaseInvoiceDetail> PurchaseInvoiceDetails { get; set; } = new();
        public List<PurchaseReturn> PurchaseReturns { get; set; } = new();
    }

    public class PurchaseInvoiceDetail : BaseEntity
    {
        public int PurchaseInvoiceId { get; set; }
        public PurchaseInvoice PurchaseInvoice { get; set; } = null!;

        public int ItemId { get; set; }
        public Item Item { get; set; } = null!;

        public int UnitId { get; set; }
        public Unit Unit { get; set; } = null!;

        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal DiscountAmount { get; set; } = 0;
        public decimal DiscountPercent { get; set; } = 0;
        public decimal TaxAmount { get; set; } = 0;
        public decimal TaxPercent { get; set; } = 0;
        public decimal LineTotal { get; set; }

        public DateTime? ExpiryDate { get; set; }

        [StringLength(50)]
        public string? BatchNumber { get; set; }

        [StringLength(50)]
        public string? SerialNumber { get; set; }

        [StringLength(200)]
        public string? Notes { get; set; }

        public List<PurchaseReturnDetail> PurchaseReturnDetails { get; set; } = new();
    }

    public class PurchaseReturn : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string ReturnNumber { get; set; } = string.Empty;

        public DateTime ReturnDate { get; set; } = DateTime.Today;

        public int PurchaseInvoiceId { get; set; }
        public PurchaseInvoice PurchaseInvoice { get; set; } = null!;

        public int SupplierId { get; set; }
        public Supplier Supplier { get; set; } = null!;

        public int WarehouseId { get; set; }
        public Warehouse Warehouse { get; set; } = null!;

        public decimal SubTotal { get; set; } = 0;
        public decimal DiscountAmount { get; set; } = 0;
        public decimal TaxAmount { get; set; } = 0;
        public decimal TotalAmount { get; set; } = 0;
        public decimal RefundAmount { get; set; } = 0;

        [StringLength(20)]
        public string Status { get; set; } = "Draft"; // Draft, Posted, Cancelled

        [StringLength(200)]
        public string? Reason { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        public int? PostedBy { get; set; }
        public DateTime? PostedDate { get; set; }

        public List<PurchaseReturnDetail> PurchaseReturnDetails { get; set; } = new();
    }

    public class PurchaseReturnDetail : BaseEntity
    {
        public int PurchaseReturnId { get; set; }
        public PurchaseReturn PurchaseReturn { get; set; } = null!;

        public int PurchaseInvoiceDetailId { get; set; }
        public PurchaseInvoiceDetail PurchaseInvoiceDetail { get; set; } = null!;

        public int ItemId { get; set; }
        public Item Item { get; set; } = null!;

        public int UnitId { get; set; }
        public Unit Unit { get; set; } = null!;

        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal DiscountAmount { get; set; } = 0;
        public decimal TaxAmount { get; set; } = 0;
        public decimal LineTotal { get; set; }

        public DateTime? ExpiryDate { get; set; }

        [StringLength(50)]
        public string? BatchNumber { get; set; }

        [StringLength(50)]
        public string? SerialNumber { get; set; }

        [StringLength(200)]
        public string? Notes { get; set; }
    }
}
