using System;
using System.ComponentModel.DataAnnotations;

namespace POSSystem.Models
{
    public class Customer : BaseEntity
    {
        [Required(ErrorMessage = "كود العميل مطلوب")]
        [StringLength(20, ErrorMessage = "كود العميل لا يمكن أن يزيد عن 20 حرف")]
        public string CustomerCode { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم العميل مطلوب")]
        [StringLength(100, ErrorMessage = "اسم العميل لا يمكن أن يزيد عن 100 حرف")]
        public string CustomerName { get; set; } = string.Empty;

        [StringLength(100)]
        public string? ContactPerson { get; set; }

        [StringLength(200)]
        public string? Address { get; set; }

        [StringLength(20)]
        public string? Phone { get; set; }

        [StringLength(20)]
        public string? Mobile { get; set; }

        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [StringLength(100)]
        public string? Email { get; set; }

        [StringLength(50)]
        public string? TaxNumber { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "حد الائتمان يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal CreditLimit { get; set; }

        [Range(0, 365, ErrorMessage = "شروط الدفع يجب أن تكون بين 0 و 365 يوم")]
        public int PaymentTerms { get; set; }

        public decimal OpeningBalance { get; set; }
        public decimal CurrentBalance { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }
    }

    public class Supplier : BaseEntity
    {
        [Required(ErrorMessage = "كود المورد مطلوب")]
        [StringLength(20, ErrorMessage = "كود المورد لا يمكن أن يزيد عن 20 حرف")]
        public string SupplierCode { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم المورد مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المورد لا يمكن أن يزيد عن 100 حرف")]
        public string SupplierName { get; set; } = string.Empty;

        [StringLength(100)]
        public string? ContactPerson { get; set; }

        [StringLength(200)]
        public string? Address { get; set; }

        [StringLength(20)]
        public string? Phone { get; set; }

        [StringLength(20)]
        public string? Mobile { get; set; }

        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [StringLength(100)]
        public string? Email { get; set; }

        [StringLength(50)]
        public string? TaxNumber { get; set; }

        [Range(0, 365, ErrorMessage = "شروط الدفع يجب أن تكون بين 0 و 365 يوم")]
        public int PaymentTerms { get; set; }

        public decimal OpeningBalance { get; set; }
        public decimal CurrentBalance { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }
    }

    public class SalesRep : BaseEntity
    {
        [Required(ErrorMessage = "كود المندوب مطلوب")]
        [StringLength(20, ErrorMessage = "كود المندوب لا يمكن أن يزيد عن 20 حرف")]
        public string SalesRepCode { get; set; } = string.Empty;

        [Required(ErrorMessage = "اسم المندوب مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المندوب لا يمكن أن يزيد عن 100 حرف")]
        public string SalesRepName { get; set; } = string.Empty;

        [StringLength(20)]
        public string? Phone { get; set; }

        [StringLength(20)]
        public string? Mobile { get; set; }

        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [StringLength(100)]
        public string? Email { get; set; }

        [StringLength(200)]
        public string? Address { get; set; }

        [Range(0, 100, ErrorMessage = "نسبة العمولة يجب أن تكون بين 0 و 100")]
        public decimal CommissionRate { get; set; }

        public decimal OpeningBalance { get; set; }
        public decimal CurrentBalance { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }
    }
}
