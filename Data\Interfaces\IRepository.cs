using POSSystem.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace POSSystem.Data.Interfaces
{
    public interface IRepository<T> where T : BaseEntity
    {
        Task<IEnumerable<T>> GetAllAsync();
        Task<T?> GetByIdAsync(int id);
        Task<int> AddAsync(T entity);
        Task<bool> UpdateAsync(T entity);
        Task<bool> DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
        Task<IEnumerable<T>> SearchAsync(string searchTerm);
    }

    public interface ICustomerRepository : IRepository<Customer>
    {
        Task<Customer?> GetByCodeAsync(string customerCode);
        Task<bool> IsCodeExistsAsync(string customerCode, int? excludeId = null);
        Task<IEnumerable<Customer>> GetActiveCustomersAsync();
        Task<decimal> GetCustomerBalanceAsync(int customerId);
        Task<bool> UpdateBalanceAsync(int customerId, decimal amount);
    }

    public interface ISupplierRepository : IRepository<Supplier>
    {
        Task<Supplier?> GetByCodeAsync(string supplierCode);
        Task<bool> IsCodeExistsAsync(string supplierCode, int? excludeId = null);
        Task<IEnumerable<Supplier>> GetActiveSuppliersAsync();
        Task<decimal> GetSupplierBalanceAsync(int supplierId);
        Task<bool> UpdateBalanceAsync(int supplierId, decimal amount);
    }

    public interface ISalesRepRepository : IRepository<SalesRep>
    {
        Task<SalesRep?> GetByCodeAsync(string salesRepCode);
        Task<bool> IsCodeExistsAsync(string salesRepCode, int? excludeId = null);
        Task<IEnumerable<SalesRep>> GetActiveSalesRepsAsync();
        Task<decimal> GetSalesRepBalanceAsync(int salesRepId);
        Task<bool> UpdateBalanceAsync(int salesRepId, decimal amount);
    }

    public interface IItemRepository : IRepository<Item>
    {
        Task<Item?> GetByCodeAsync(string itemCode);
        Task<Item?> GetByBarcodeAsync(string barcode);
        Task<bool> IsCodeExistsAsync(string itemCode, int? excludeId = null);
        Task<bool> IsBarcodeExistsAsync(string barcode, int? excludeId = null);
        Task<IEnumerable<Item>> GetActiveItemsAsync();
        Task<IEnumerable<Item>> GetItemsByCategoryAsync(int categoryId);
        Task<IEnumerable<ItemUnit>> GetItemUnitsAsync(int itemId);
        Task<ItemStock?> GetItemStockAsync(int itemId, int warehouseId, int unitId);
        Task<bool> UpdateStockAsync(int itemId, int warehouseId, int unitId, decimal quantity, decimal cost);
    }

    public interface IWarehouseRepository : IRepository<Warehouse>
    {
        Task<Warehouse?> GetByCodeAsync(string warehouseCode);
        Task<bool> IsCodeExistsAsync(string warehouseCode, int? excludeId = null);
        Task<IEnumerable<Warehouse>> GetActiveWarehousesAsync();
    }

    public interface ITreasuryRepository : IRepository<Treasury>
    {
        Task<Treasury?> GetByCodeAsync(string treasuryCode);
        Task<bool> IsCodeExistsAsync(string treasuryCode, int? excludeId = null);
        Task<IEnumerable<Treasury>> GetActiveTreasuriesAsync();
        Task<decimal> GetTreasuryBalanceAsync(int treasuryId);
        Task<bool> UpdateBalanceAsync(int treasuryId, decimal amount);
    }

    public interface IUnitRepository : IRepository<Unit>
    {
        Task<Unit?> GetByCodeAsync(string unitCode);
        Task<bool> IsCodeExistsAsync(string unitCode, int? excludeId = null);
        Task<IEnumerable<Unit>> GetActiveUnitsAsync();
    }

    public interface IItemCategoryRepository : IRepository<ItemCategory>
    {
        Task<ItemCategory?> GetByCodeAsync(string categoryCode);
        Task<bool> IsCodeExistsAsync(string categoryCode, int? excludeId = null);
        Task<IEnumerable<ItemCategory>> GetActiveCategoriesAsync();
        Task<IEnumerable<ItemCategory>> GetMainCategoriesAsync();
        Task<IEnumerable<ItemCategory>> GetSubCategoriesAsync(int parentCategoryId);
    }

    public interface IUserRepository : IRepository<User>
    {
        Task<User?> GetByUsernameAsync(string username);
        Task<bool> IsUsernameExistsAsync(string username, int? excludeId = null);
        Task<bool> ValidatePasswordAsync(string username, string password);
        Task<bool> UpdatePasswordAsync(int userId, string newPassword);
        Task<bool> UpdateLastLoginAsync(int userId);
        Task<IEnumerable<UserPermission>> GetUserPermissionsAsync(int userId);
    }

    public interface ISalesInvoiceRepository : IRepository<SalesInvoice>
    {
        Task<SalesInvoice?> GetByNumberAsync(string invoiceNumber);
        Task<bool> IsNumberExistsAsync(string invoiceNumber, int? excludeId = null);
        Task<string> GetNextInvoiceNumberAsync();
        Task<IEnumerable<SalesInvoice>> GetInvoicesByCustomerAsync(int customerId);
        Task<IEnumerable<SalesInvoice>> GetInvoicesByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<SalesInvoiceDetail>> GetInvoiceDetailsAsync(int invoiceId);
        Task<bool> PostInvoiceAsync(int invoiceId, int userId);
    }

    public interface IPurchaseInvoiceRepository : IRepository<PurchaseInvoice>
    {
        Task<PurchaseInvoice?> GetByNumberAsync(string invoiceNumber);
        Task<bool> IsNumberExistsAsync(string invoiceNumber, int? excludeId = null);
        Task<string> GetNextInvoiceNumberAsync();
        Task<IEnumerable<PurchaseInvoice>> GetInvoicesBySupplierAsync(int supplierId);
        Task<IEnumerable<PurchaseInvoice>> GetInvoicesByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<PurchaseInvoiceDetail>> GetInvoiceDetailsAsync(int invoiceId);
        Task<bool> PostInvoiceAsync(int invoiceId, int userId);
    }

    public interface IQuotationRepository : IRepository<Quotation>
    {
        Task<Quotation?> GetByNumberAsync(string quotationNumber);
        Task<bool> IsNumberExistsAsync(string quotationNumber, int? excludeId = null);
        Task<string> GetNextQuotationNumberAsync();
        Task<IEnumerable<Quotation>> GetQuotationsByCustomerAsync(int customerId);
        Task<IEnumerable<Quotation>> GetQuotationsByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<QuotationDetail>> GetQuotationDetailsAsync(int quotationId);
    }

    public interface IFinancialTransactionRepository : IRepository<FinancialTransaction>
    {
        Task<FinancialTransaction?> GetByNumberAsync(string transactionNumber);
        Task<bool> IsNumberExistsAsync(string transactionNumber, int? excludeId = null);
        Task<string> GetNextTransactionNumberAsync(string transactionType);
        Task<IEnumerable<FinancialTransaction>> GetTransactionsByTreasuryAsync(int treasuryId);
        Task<IEnumerable<FinancialTransaction>> GetTransactionsByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<bool> PostTransactionAsync(int transactionId, int userId);
    }

    public interface IStockMovementRepository : IRepository<StockMovement>
    {
        Task<IEnumerable<StockMovement>> GetMovementsByItemAsync(int itemId);
        Task<IEnumerable<StockMovement>> GetMovementsByWarehouseAsync(int warehouseId);
        Task<IEnumerable<StockMovement>> GetMovementsByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<bool> AddMovementAsync(StockMovement movement);
    }

    public interface IReportRepository
    {
        Task<DataTable> GetCustomerStatementAsync(int customerId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<DataTable> GetSupplierStatementAsync(int supplierId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<DataTable> GetSalesRepStatementAsync(int salesRepId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<DataTable> GetItemMovementReportAsync(int itemId, int? warehouseId = null, DateTime? fromDate = null, DateTime? toDate = null);
        Task<DataTable> GetStockReportAsync(int? warehouseId = null, int? categoryId = null);
        Task<DataTable> GetSalesReportAsync(DateTime fromDate, DateTime toDate, int? customerId = null, int? salesRepId = null);
        Task<DataTable> GetPurchaseReportAsync(DateTime fromDate, DateTime toDate, int? supplierId = null);
        Task<DataTable> GetProfitLossReportAsync(DateTime fromDate, DateTime toDate);
        Task<DataTable> GetBalanceSheetAsync(DateTime asOfDate);
        Task<DataTable> GetTrialBalanceAsync(DateTime fromDate, DateTime toDate);
    }
}
