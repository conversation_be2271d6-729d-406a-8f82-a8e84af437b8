using Microsoft.Extensions.Logging;
using POSSystem.Data.Interfaces;
using POSSystem.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;

namespace POSSystem.Data.Repositories
{
    public class CustomerRepository : ICustomerRepository
    {
        private readonly DatabaseManager _dbManager;
        private readonly ILogger<CustomerRepository> _logger;

        public CustomerRepository(DatabaseManager dbManager, ILogger<CustomerRepository> logger)
        {
            _dbManager = dbManager;
            _logger = logger;
        }

        public async Task<IEnumerable<Customer>> GetAllAsync()
        {
            try
            {
                var query = @"
                    SELECT CustomerID, CustomerCode, CustomerName, ContactPerson, Address, 
                           Phone, Mobile, Email, TaxNumber, CreditLimit, PaymentTerms, 
                           OpeningBalance, CurrentBalance, IsActive, CreatedDate, Notes
                    FROM Customers 
                    ORDER BY CustomerName";

                var dataTable = await _dbManager.ExecuteQueryAsync(query);
                return MapDataTableToCustomers(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب جميع العملاء");
                throw;
            }
        }

        public async Task<Customer?> GetByIdAsync(int id)
        {
            try
            {
                var query = @"
                    SELECT CustomerID, CustomerCode, CustomerName, ContactPerson, Address, 
                           Phone, Mobile, Email, TaxNumber, CreditLimit, PaymentTerms, 
                           OpeningBalance, CurrentBalance, IsActive, CreatedDate, Notes
                    FROM Customers 
                    WHERE CustomerID = @CustomerID";

                var parameters = new[] { new SqlParameter("@CustomerID", id) };
                var dataTable = await _dbManager.ExecuteQueryAsync(query, parameters);

                if (dataTable.Rows.Count == 0)
                    return null;

                return MapDataRowToCustomer(dataTable.Rows[0]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب العميل برقم {CustomerId}", id);
                throw;
            }
        }

        public async Task<Customer?> GetByCodeAsync(string customerCode)
        {
            try
            {
                var query = @"
                    SELECT CustomerID, CustomerCode, CustomerName, ContactPerson, Address, 
                           Phone, Mobile, Email, TaxNumber, CreditLimit, PaymentTerms, 
                           OpeningBalance, CurrentBalance, IsActive, CreatedDate, Notes
                    FROM Customers 
                    WHERE CustomerCode = @CustomerCode";

                var parameters = new[] { new SqlParameter("@CustomerCode", customerCode) };
                var dataTable = await _dbManager.ExecuteQueryAsync(query, parameters);

                if (dataTable.Rows.Count == 0)
                    return null;

                return MapDataRowToCustomer(dataTable.Rows[0]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب العميل بالكود {CustomerCode}", customerCode);
                throw;
            }
        }

        public async Task<int> AddAsync(Customer entity)
        {
            try
            {
                var query = @"
                    INSERT INTO Customers (CustomerCode, CustomerName, ContactPerson, Address, 
                                         Phone, Mobile, Email, TaxNumber, CreditLimit, PaymentTerms, 
                                         OpeningBalance, CurrentBalance, IsActive, CreatedDate, Notes)
                    VALUES (@CustomerCode, @CustomerName, @ContactPerson, @Address, 
                            @Phone, @Mobile, @Email, @TaxNumber, @CreditLimit, @PaymentTerms, 
                            @OpeningBalance, @CurrentBalance, @IsActive, @CreatedDate, @Notes);
                    SELECT SCOPE_IDENTITY();";

                var parameters = new[]
                {
                    new SqlParameter("@CustomerCode", entity.CustomerCode),
                    new SqlParameter("@CustomerName", entity.CustomerName),
                    new SqlParameter("@ContactPerson", (object?)entity.ContactPerson ?? DBNull.Value),
                    new SqlParameter("@Address", (object?)entity.Address ?? DBNull.Value),
                    new SqlParameter("@Phone", (object?)entity.Phone ?? DBNull.Value),
                    new SqlParameter("@Mobile", (object?)entity.Mobile ?? DBNull.Value),
                    new SqlParameter("@Email", (object?)entity.Email ?? DBNull.Value),
                    new SqlParameter("@TaxNumber", (object?)entity.TaxNumber ?? DBNull.Value),
                    new SqlParameter("@CreditLimit", entity.CreditLimit),
                    new SqlParameter("@PaymentTerms", entity.PaymentTerms),
                    new SqlParameter("@OpeningBalance", entity.OpeningBalance),
                    new SqlParameter("@CurrentBalance", entity.CurrentBalance),
                    new SqlParameter("@IsActive", entity.IsActive),
                    new SqlParameter("@CreatedDate", entity.CreatedDate),
                    new SqlParameter("@Notes", (object?)entity.Notes ?? DBNull.Value)
                };

                var result = await _dbManager.ExecuteScalarAsync(query, parameters);
                var customerId = Convert.ToInt32(result);

                _logger.LogInformation("تم إضافة العميل {CustomerName} بنجاح برقم {CustomerId}", entity.CustomerName, customerId);
                return customerId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة العميل {CustomerName}", entity.CustomerName);
                throw;
            }
        }

        public async Task<bool> UpdateAsync(Customer entity)
        {
            try
            {
                var query = @"
                    UPDATE Customers 
                    SET CustomerCode = @CustomerCode, CustomerName = @CustomerName, 
                        ContactPerson = @ContactPerson, Address = @Address, 
                        Phone = @Phone, Mobile = @Mobile, Email = @Email, 
                        TaxNumber = @TaxNumber, CreditLimit = @CreditLimit, 
                        PaymentTerms = @PaymentTerms, OpeningBalance = @OpeningBalance, 
                        CurrentBalance = @CurrentBalance, IsActive = @IsActive, Notes = @Notes
                    WHERE CustomerID = @CustomerID";

                var parameters = new[]
                {
                    new SqlParameter("@CustomerID", entity.Id),
                    new SqlParameter("@CustomerCode", entity.CustomerCode),
                    new SqlParameter("@CustomerName", entity.CustomerName),
                    new SqlParameter("@ContactPerson", (object?)entity.ContactPerson ?? DBNull.Value),
                    new SqlParameter("@Address", (object?)entity.Address ?? DBNull.Value),
                    new SqlParameter("@Phone", (object?)entity.Phone ?? DBNull.Value),
                    new SqlParameter("@Mobile", (object?)entity.Mobile ?? DBNull.Value),
                    new SqlParameter("@Email", (object?)entity.Email ?? DBNull.Value),
                    new SqlParameter("@TaxNumber", (object?)entity.TaxNumber ?? DBNull.Value),
                    new SqlParameter("@CreditLimit", entity.CreditLimit),
                    new SqlParameter("@PaymentTerms", entity.PaymentTerms),
                    new SqlParameter("@OpeningBalance", entity.OpeningBalance),
                    new SqlParameter("@CurrentBalance", entity.CurrentBalance),
                    new SqlParameter("@IsActive", entity.IsActive),
                    new SqlParameter("@Notes", (object?)entity.Notes ?? DBNull.Value)
                };

                var rowsAffected = await _dbManager.ExecuteNonQueryAsync(query, parameters);
                var success = rowsAffected > 0;

                if (success)
                    _logger.LogInformation("تم تحديث العميل {CustomerName} بنجاح", entity.CustomerName);

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث العميل {CustomerName}", entity.CustomerName);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                var query = "UPDATE Customers SET IsActive = 0 WHERE CustomerID = @CustomerID";
                var parameters = new[] { new SqlParameter("@CustomerID", id) };

                var rowsAffected = await _dbManager.ExecuteNonQueryAsync(query, parameters);
                var success = rowsAffected > 0;

                if (success)
                    _logger.LogInformation("تم حذف العميل برقم {CustomerId} بنجاح", id);

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف العميل برقم {CustomerId}", id);
                throw;
            }
        }

        public async Task<bool> ExistsAsync(int id)
        {
            try
            {
                var query = "SELECT COUNT(*) FROM Customers WHERE CustomerID = @CustomerID";
                var parameters = new[] { new SqlParameter("@CustomerID", id) };

                var result = await _dbManager.ExecuteScalarAsync(query, parameters);
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من وجود العميل برقم {CustomerId}", id);
                throw;
            }
        }

        public async Task<bool> IsCodeExistsAsync(string customerCode, int? excludeId = null)
        {
            try
            {
                var query = "SELECT COUNT(*) FROM Customers WHERE CustomerCode = @CustomerCode";
                var parameters = new List<SqlParameter> { new("@CustomerCode", customerCode) };

                if (excludeId.HasValue)
                {
                    query += " AND CustomerID != @ExcludeId";
                    parameters.Add(new SqlParameter("@ExcludeId", excludeId.Value));
                }

                var result = await _dbManager.ExecuteScalarAsync(query, parameters.ToArray());
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من وجود كود العميل {CustomerCode}", customerCode);
                throw;
            }
        }

        public async Task<IEnumerable<Customer>> GetActiveCustomersAsync()
        {
            try
            {
                var query = @"
                    SELECT CustomerID, CustomerCode, CustomerName, ContactPerson, Address, 
                           Phone, Mobile, Email, TaxNumber, CreditLimit, PaymentTerms, 
                           OpeningBalance, CurrentBalance, IsActive, CreatedDate, Notes
                    FROM Customers 
                    WHERE IsActive = 1
                    ORDER BY CustomerName";

                var dataTable = await _dbManager.ExecuteQueryAsync(query);
                return MapDataTableToCustomers(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب العملاء النشطين");
                throw;
            }
        }

        public async Task<IEnumerable<Customer>> SearchAsync(string searchTerm)
        {
            try
            {
                var query = @"
                    SELECT CustomerID, CustomerCode, CustomerName, ContactPerson, Address, 
                           Phone, Mobile, Email, TaxNumber, CreditLimit, PaymentTerms, 
                           OpeningBalance, CurrentBalance, IsActive, CreatedDate, Notes
                    FROM Customers 
                    WHERE CustomerCode LIKE @SearchTerm 
                       OR CustomerName LIKE @SearchTerm 
                       OR Phone LIKE @SearchTerm 
                       OR Mobile LIKE @SearchTerm
                    ORDER BY CustomerName";

                var parameters = new[] { new SqlParameter("@SearchTerm", $"%{searchTerm}%") };
                var dataTable = await _dbManager.ExecuteQueryAsync(query, parameters);
                return MapDataTableToCustomers(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن العملاء بالمصطلح {SearchTerm}", searchTerm);
                throw;
            }
        }

        public async Task<decimal> GetCustomerBalanceAsync(int customerId)
        {
            try
            {
                var query = "SELECT CurrentBalance FROM Customers WHERE CustomerID = @CustomerID";
                var parameters = new[] { new SqlParameter("@CustomerID", customerId) };

                var result = await _dbManager.ExecuteScalarAsync(query, parameters);
                return result != null ? Convert.ToDecimal(result) : 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب رصيد العميل برقم {CustomerId}", customerId);
                throw;
            }
        }

        public async Task<bool> UpdateBalanceAsync(int customerId, decimal amount)
        {
            try
            {
                var query = @"
                    UPDATE Customers 
                    SET CurrentBalance = CurrentBalance + @Amount 
                    WHERE CustomerID = @CustomerID";

                var parameters = new[]
                {
                    new SqlParameter("@CustomerID", customerId),
                    new SqlParameter("@Amount", amount)
                };

                var rowsAffected = await _dbManager.ExecuteNonQueryAsync(query, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث رصيد العميل برقم {CustomerId}", customerId);
                throw;
            }
        }

        private static IEnumerable<Customer> MapDataTableToCustomers(DataTable dataTable)
        {
            var customers = new List<Customer>();
            foreach (DataRow row in dataTable.Rows)
            {
                customers.Add(MapDataRowToCustomer(row));
            }
            return customers;
        }

        private static Customer MapDataRowToCustomer(DataRow row)
        {
            return new Customer
            {
                Id = Convert.ToInt32(row["CustomerID"]),
                CustomerCode = row["CustomerCode"].ToString()!,
                CustomerName = row["CustomerName"].ToString()!,
                ContactPerson = row["ContactPerson"] as string,
                Address = row["Address"] as string,
                Phone = row["Phone"] as string,
                Mobile = row["Mobile"] as string,
                Email = row["Email"] as string,
                TaxNumber = row["TaxNumber"] as string,
                CreditLimit = Convert.ToDecimal(row["CreditLimit"]),
                PaymentTerms = Convert.ToInt32(row["PaymentTerms"]),
                OpeningBalance = Convert.ToDecimal(row["OpeningBalance"]),
                CurrentBalance = Convert.ToDecimal(row["CurrentBalance"]),
                IsActive = Convert.ToBoolean(row["IsActive"]),
                CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                Notes = row["Notes"] as string
            };
        }
    }
}
