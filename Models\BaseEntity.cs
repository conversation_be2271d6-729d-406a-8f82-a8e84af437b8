using System;
using System.ComponentModel.DataAnnotations;

namespace POSSystem.Models
{
    public abstract class BaseEntity
    {
        public int Id { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public int? CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public int? ModifiedBy { get; set; }
    }

    public class Customer : BaseEntity
    {
        [Required]
        [StringLength(20)]
        public string CustomerCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string CustomerName { get; set; } = string.Empty;

        [StringLength(100)]
        public string? ContactPerson { get; set; }

        [StringLength(200)]
        public string? Address { get; set; }

        [StringLength(20)]
        public string? Phone { get; set; }

        [StringLength(20)]
        public string? Mobile { get; set; }

        [StringLength(100)]
        [EmailAddress]
        public string? Email { get; set; }

        [StringLength(50)]
        public string? TaxNumber { get; set; }

        public decimal CreditLimit { get; set; } = 0;
        public int PaymentTerms { get; set; } = 0; // عدد الأيام
        public decimal OpeningBalance { get; set; } = 0;
        public decimal CurrentBalance { get; set; } = 0;

        [StringLength(500)]
        public string? Notes { get; set; }
    }

    public class Supplier : BaseEntity
    {
        [Required]
        [StringLength(20)]
        public string SupplierCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string SupplierName { get; set; } = string.Empty;

        [StringLength(100)]
        public string? ContactPerson { get; set; }

        [StringLength(200)]
        public string? Address { get; set; }

        [StringLength(20)]
        public string? Phone { get; set; }

        [StringLength(20)]
        public string? Mobile { get; set; }

        [StringLength(100)]
        [EmailAddress]
        public string? Email { get; set; }

        [StringLength(50)]
        public string? TaxNumber { get; set; }

        public int PaymentTerms { get; set; } = 0;
        public decimal OpeningBalance { get; set; } = 0;
        public decimal CurrentBalance { get; set; } = 0;

        [StringLength(500)]
        public string? Notes { get; set; }
    }

    public class SalesRep : BaseEntity
    {
        [Required]
        [StringLength(20)]
        public string SalesRepCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string SalesRepName { get; set; } = string.Empty;

        [StringLength(20)]
        public string? Phone { get; set; }

        [StringLength(20)]
        public string? Mobile { get; set; }

        [StringLength(100)]
        [EmailAddress]
        public string? Email { get; set; }

        [StringLength(200)]
        public string? Address { get; set; }

        public decimal CommissionRate { get; set; } = 0;
        public decimal OpeningBalance { get; set; } = 0;
        public decimal CurrentBalance { get; set; } = 0;

        [StringLength(500)]
        public string? Notes { get; set; }
    }

    public class Warehouse : BaseEntity
    {
        [Required]
        [StringLength(20)]
        public string WarehouseCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string WarehouseName { get; set; } = string.Empty;

        [StringLength(200)]
        public string? Location { get; set; }

        [StringLength(100)]
        public string? Manager { get; set; }

        [StringLength(20)]
        public string? Phone { get; set; }
    }

    public class Treasury : BaseEntity
    {
        [Required]
        [StringLength(20)]
        public string TreasuryCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string TreasuryName { get; set; } = string.Empty;

        public decimal OpeningBalance { get; set; } = 0;
        public decimal CurrentBalance { get; set; } = 0;
    }

    public class ItemCategory : BaseEntity
    {
        [Required]
        [StringLength(20)]
        public string CategoryCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string CategoryName { get; set; } = string.Empty;

        public int? ParentCategoryId { get; set; }
        public ItemCategory? ParentCategory { get; set; }
        public List<ItemCategory> SubCategories { get; set; } = new();
        public List<Item> Items { get; set; } = new();
    }

    public class Unit : BaseEntity
    {
        [Required]
        [StringLength(10)]
        public string UnitCode { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string UnitName { get; set; } = string.Empty;
    }

    public class Item : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string ItemCode { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string ItemName { get; set; } = string.Empty;

        [StringLength(200)]
        public string? ItemNameEn { get; set; }

        public int? CategoryId { get; set; }
        public ItemCategory? Category { get; set; }

        public int BaseUnitId { get; set; }
        public Unit BaseUnit { get; set; } = null!;

        [StringLength(50)]
        public string? Barcode { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        public decimal MinStockLevel { get; set; } = 0;
        public decimal MaxStockLevel { get; set; } = 0;
        public decimal ReorderLevel { get; set; } = 0;
        public bool HasExpiry { get; set; } = false;
        public bool IsSerial { get; set; } = false;

        public List<ItemUnit> ItemUnits { get; set; } = new();
        public List<ItemStock> ItemStocks { get; set; } = new();
    }

    public class ItemUnit
    {
        public int ItemId { get; set; }
        public Item Item { get; set; } = null!;

        public int UnitId { get; set; }
        public Unit Unit { get; set; } = null!;

        public decimal ConversionFactor { get; set; } = 1;
        public decimal PurchasePrice { get; set; } = 0;
        public decimal SalePrice { get; set; } = 0;
        public decimal WholesalePrice { get; set; } = 0;
        public bool IsDefault { get; set; } = false;
    }

    public class ItemStock
    {
        public int ItemId { get; set; }
        public Item Item { get; set; } = null!;

        public int WarehouseId { get; set; }
        public Warehouse Warehouse { get; set; } = null!;

        public int UnitId { get; set; }
        public Unit Unit { get; set; } = null!;

        public decimal QuantityOnHand { get; set; } = 0;
        public decimal QuantityReserved { get; set; } = 0;
        public decimal QuantityAvailable { get; set; } = 0;
        public decimal AverageCost { get; set; } = 0;
        public decimal LastCost { get; set; } = 0;
        public DateTime LastUpdateDate { get; set; } = DateTime.Now;
    }

    public class User : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string Password { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string FullName { get; set; } = string.Empty;

        [StringLength(100)]
        [EmailAddress]
        public string? Email { get; set; }

        [StringLength(20)]
        public string? Phone { get; set; }

        public DateTime? LastLoginDate { get; set; }

        public List<UserPermission> UserPermissions { get; set; } = new();
    }

    public class Permission : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string PermissionName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string PermissionCode { get; set; } = string.Empty;

        [StringLength(200)]
        public string? Description { get; set; }

        public List<UserPermission> UserPermissions { get; set; } = new();
    }

    public class UserPermission
    {
        public int UserId { get; set; }
        public User User { get; set; } = null!;

        public int PermissionId { get; set; }
        public Permission Permission { get; set; } = null!;

        public bool CanView { get; set; } = false;
        public bool CanAdd { get; set; } = false;
        public bool CanEdit { get; set; } = false;
        public bool CanDelete { get; set; } = false;
    }
}
