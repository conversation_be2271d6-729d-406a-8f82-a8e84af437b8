# نظام إدارة المبيعات والمشتريات والمخازن
## Point of Sale and Inventory Management System

نظام شامل لإدارة المبيعات والمشتريات والمخازن مطور باستخدام C# و Windows Forms مع قاعدة بيانات SQL Server.

## المميزات الرئيسية

### 📊 البيانات الأساسية
- **إدارة العملاء**: إضافة وتعديل وحذف العملاء مع الأرصدة السابقة
- **إدارة الموردين**: إدارة شاملة للموردين مع شروط الدفع
- **إدارة المناديب**: متابعة المناديب والعمولات
- **إدارة الأصناف**: أصناف متعددة الأسعار والوحدات مع الباركود
- **إدارة المخازن**: متعدد المخازن مع التحكم في المواقع
- **إدارة الخزائن**: متعدد الخزائن مع الأرصدة

### 💰 المبيعات
- **عروض الأسعار**: إنشاء وإدارة عروض الأسعار للعملاء
- **فواتير البيع**: فواتير مفصلة مع الضرائب والخصومات
- **مرتجعات البيع**: إدارة مرتجعات المبيعات مع استرداد المخزون

### 🛒 المشتريات
- **طلبيات الشراء**: إنشاء ومتابعة طلبيات الشراء
- **فواتير الشراء**: استلام البضائع وإدارة فواتير الموردين
- **مرتجعات الشراء**: إرجاع البضائع للموردين

### 💳 الإدارة المالية
- **سندات القبض**: استلام المدفوعات من العملاء
- **سندات الصرف**: دفع المستحقات للموردين
- **سندات الاستلام النقدي**: إدارة الإيرادات النقدية
- **سندات الدفع النقدي**: إدارة المصروفات النقدية
- **التحويل بين الخزائن**: نقل الأموال بين الخزائن المختلفة

### 📦 إدارة المخازن
- **جرد المخازن**: عمليات الجرد الدورية والفورية
- **التحويل بين المخازن**: نقل البضائع بين المخازن
- **حركة الأصناف**: متابعة تفصيلية لحركة كل صنف
- **الأرصدة الافتتاحية**: إدخال أرصدة البداية للأصناف

### 👥 إدارة المستخدمين والصلاحيات
- **المستخدمين**: إدارة حسابات المستخدمين
- **الصلاحيات**: تحديد صلاحيات كل مستخدم بدقة
- **تسجيل الدخول**: نظام آمن لتسجيل الدخول

### 📈 التقارير الشاملة
- **كشف حساب العملاء**: تقارير مفصلة لحسابات العملاء
- **كشف حساب الموردين**: متابعة المستحقات للموردين
- **كشف حساب المناديب**: تقارير العمولات والمستحقات
- **تقرير الأرباح والخسائر**: تحليل الربحية
- **المركز المالي**: الوضع المالي للشركة
- **ميزان المراجعة**: التقارير المحاسبية
- **حركة الأصناف**: تقارير تفصيلية لحركة المخزون
- **النواقص**: تقارير الأصناف الناقصة
- **الأصناف المنتهية الصلاحية**: متابعة تواريخ الانتهاء

## متطلبات النظام

### البرمجيات المطلوبة
- Windows 10 أو أحدث
- .NET 6.0 Runtime أو أحدث
- SQL Server 2019 أو أحدث (أو SQL Server Express)
- Visual Studio 2022 (للتطوير)

### متطلبات الأجهزة
- معالج: Intel Core i3 أو ما يعادله
- الذاكرة: 4 GB RAM كحد أدنى (8 GB مستحسن)
- مساحة القرص: 500 MB للتطبيق + مساحة لقاعدة البيانات
- الشاشة: 1024x768 كحد أدنى (1920x1080 مستحسن)

## التثبيت والإعداد

### 1. متطلبات النظام
- Windows 10 أو أحدث
- .NET 6.0 SDK أو أحدث
- SQL Server 2019 أو SQL Server Express (مجاني)

### 2. إعداد قاعدة البيانات
```sql
-- تشغيل ملف إعداد قاعدة البيانات
-- setup_database.sql في SQL Server Management Studio
```

أو استخدم الأمر التالي في Command Prompt:
```cmd
sqlcmd -S .\SQLEXPRESS -i setup_database.sql
```

### 3. تكوين الاتصال
قم بتعديل ملف `appsettings.json` حسب إعدادات SQL Server لديك:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=.\\SQLEXPRESS;Database=POSSystem;Integrated Security=true;TrustServerCertificate=true;"
  }
}
```

### 4. تشغيل التطبيق

#### الطريقة الأولى: استخدام ملف التشغيل السريع
```cmd
run.bat
```

#### الطريقة الثانية: استخدام .NET CLI
```bash
dotnet restore
dotnet build
dotnet run
```

## بيانات الدخول الافتراضية
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## هيكل المشروع

```
POSSystem/
├── Database/           # ملفات قاعدة البيانات
├── Models/            # نماذج البيانات
├── Data/              # طبقة الوصول للبيانات
│   ├── Interfaces/    # واجهات Repository
│   └── Repositories/  # تطبيق Repository
├── Services/          # طبقة منطق الأعمال
├── Forms/             # نماذج واجهة المستخدم
├── Reports/           # التقارير
└── Utils/             # الأدوات المساعدة
```

## التقنيات المستخدمة

- **C# .NET 6**: لغة البرمجة والإطار
- **Windows Forms**: واجهة المستخدم
- **SQL Server**: قاعدة البيانات
- **ADO.NET**: الوصول لقاعدة البيانات
- **Dependency Injection**: حقن التبعيات
- **Serilog**: تسجيل الأحداث
- **Repository Pattern**: نمط Repository
- **Service Layer**: طبقة الخدمات

## المساهمة في المشروع

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم والمساعدة

للحصول على الدعم أو الإبلاغ عن مشاكل:
- فتح Issue في GitHub
- التواصل عبر البريد الإلكتروني

## خارطة الطريق

### الإصدار 1.1
- [ ] تطبيق الهاتف المحمول
- [ ] واجهة ويب
- [ ] تكامل مع أنظمة الدفع الإلكتروني
- [ ] تقارير متقدمة مع الرسوم البيانية

### الإصدار 1.2
- [ ] نظام الإشعارات
- [ ] النسخ الاحتياطي التلقائي
- [ ] تصدير البيانات لـ Excel
- [ ] طباعة الباركود

## شكر وتقدير

شكر خاص لجميع المساهمين في تطوير هذا النظام.

---

**ملاحظة**: هذا النظام في مرحلة التطوير المستمر. يرجى التحقق من التحديثات بانتظام.
