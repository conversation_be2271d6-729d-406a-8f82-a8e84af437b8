using Microsoft.Extensions.Logging;
using POSSystem.Data.Interfaces;
using POSSystem.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace POSSystem.Services
{
    public class SalesService : ISalesService
    {
        private readonly ISalesInvoiceRepository _salesInvoiceRepository;
        private readonly ICustomerRepository _customerRepository;
        private readonly IWarehouseRepository _warehouseRepository;
        private readonly IItemRepository _itemRepository;
        private readonly ILogger<SalesService> _logger;

        public SalesService(
            ISalesInvoiceRepository salesInvoiceRepository,
            ICustomerRepository customerRepository,
            IWarehouseRepository warehouseRepository,
            IItemRepository itemRepository,
            ILogger<SalesService> logger)
        {
            _salesInvoiceRepository = salesInvoiceRepository;
            _customerRepository = customerRepository;
            _warehouseRepository = warehouseRepository;
            _itemRepository = itemRepository;
            _logger = logger;
        }

        public async Task<IEnumerable<SalesInvoice>> GetAllSalesInvoicesAsync()
        {
            try
            {
                return await _salesInvoiceRepository.GetAllAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب جميع فواتير البيع");
                throw;
            }
        }

        public async Task<SalesInvoice?> GetSalesInvoiceByIdAsync(int id)
        {
            try
            {
                if (id <= 0)
                    throw new ArgumentException("رقم الفاتورة غير صحيح", nameof(id));

                return await _salesInvoiceRepository.GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب فاتورة البيع برقم {InvoiceId}", id);
                throw;
            }
        }

        public async Task<SalesInvoice?> GetSalesInvoiceByNumberAsync(string invoiceNumber)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(invoiceNumber))
                    throw new ArgumentException("رقم الفاتورة مطلوب", nameof(invoiceNumber));

                return await _salesInvoiceRepository.GetByNumberAsync(invoiceNumber);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب فاتورة البيع برقم {InvoiceNumber}", invoiceNumber);
                throw;
            }
        }

        public async Task<int> AddSalesInvoiceAsync(SalesInvoice invoice)
        {
            try
            {
                // التحقق من صحة البيانات
                await ValidateSalesInvoiceAsync(invoice);

                // التحقق من عدم تكرار رقم الفاتورة
                if (await _salesInvoiceRepository.IsNumberExistsAsync(invoice.InvoiceNumber))
                {
                    throw new InvalidOperationException($"رقم فاتورة البيع '{invoice.InvoiceNumber}' موجود مسبقاً");
                }

                // إعداد البيانات الافتراضية
                invoice.CreatedDate = DateTime.Now;
                invoice.CreatedBy = CurrentUser.User?.Id;
                invoice.Status = "Draft";
                invoice.PaymentStatus = "Pending";
                invoice.RemainingAmount = invoice.TotalAmount - invoice.PaidAmount;

                var invoiceId = await _salesInvoiceRepository.AddAsync(invoice);
                _logger.LogInformation("تم إضافة فاتورة البيع {InvoiceNumber} بنجاح برقم {InvoiceId}", invoice.InvoiceNumber, invoiceId);

                return invoiceId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة فاتورة البيع {InvoiceNumber}", invoice.InvoiceNumber);
                throw;
            }
        }

        public async Task<bool> UpdateSalesInvoiceAsync(SalesInvoice invoice)
        {
            try
            {
                // التحقق من صحة البيانات
                await ValidateSalesInvoiceAsync(invoice);

                // التحقق من وجود الفاتورة
                var existingInvoice = await _salesInvoiceRepository.GetByIdAsync(invoice.Id);
                if (existingInvoice == null)
                {
                    throw new InvalidOperationException($"فاتورة البيع برقم {invoice.Id} غير موجودة");
                }

                // التحقق من حالة الفاتورة
                if (existingInvoice.Status == "Posted")
                {
                    throw new InvalidOperationException("لا يمكن تعديل فاتورة مرحلة");
                }

                // التحقق من عدم تكرار رقم الفاتورة
                if (await _salesInvoiceRepository.IsNumberExistsAsync(invoice.InvoiceNumber, invoice.Id))
                {
                    throw new InvalidOperationException($"رقم فاتورة البيع '{invoice.InvoiceNumber}' موجود مسبقاً");
                }

                // الحفاظ على البيانات الأساسية
                invoice.CreatedDate = existingInvoice.CreatedDate;
                invoice.CreatedBy = existingInvoice.CreatedBy;
                invoice.ModifiedDate = DateTime.Now;
                invoice.ModifiedBy = CurrentUser.User?.Id;
                invoice.RemainingAmount = invoice.TotalAmount - invoice.PaidAmount;

                // تحديث حالة الدفع
                if (invoice.PaidAmount == 0)
                    invoice.PaymentStatus = "Pending";
                else if (invoice.PaidAmount >= invoice.TotalAmount)
                    invoice.PaymentStatus = "Paid";
                else
                    invoice.PaymentStatus = "Partial";

                var success = await _salesInvoiceRepository.UpdateAsync(invoice);
                if (success)
                {
                    _logger.LogInformation("تم تحديث فاتورة البيع {InvoiceNumber} بنجاح", invoice.InvoiceNumber);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث فاتورة البيع {InvoiceNumber}", invoice.InvoiceNumber);
                throw;
            }
        }

        public async Task<bool> DeleteSalesInvoiceAsync(int id)
        {
            try
            {
                if (id <= 0)
                    throw new ArgumentException("رقم الفاتورة غير صحيح", nameof(id));

                // التحقق من وجود الفاتورة
                var invoice = await _salesInvoiceRepository.GetByIdAsync(id);
                if (invoice == null)
                {
                    throw new InvalidOperationException($"فاتورة البيع برقم {id} غير موجودة");
                }

                // التحقق من حالة الفاتورة
                if (invoice.Status == "Posted")
                {
                    throw new InvalidOperationException("لا يمكن حذف فاتورة مرحلة");
                }

                var success = await _salesInvoiceRepository.DeleteAsync(id);
                if (success)
                {
                    _logger.LogInformation("تم حذف فاتورة البيع {InvoiceNumber} بنجاح", invoice.InvoiceNumber);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف فاتورة البيع برقم {InvoiceId}", id);
                throw;
            }
        }

        public async Task<bool> PostSalesInvoiceAsync(int invoiceId, int userId)
        {
            try
            {
                if (invoiceId <= 0)
                    throw new ArgumentException("رقم الفاتورة غير صحيح", nameof(invoiceId));

                // التحقق من وجود الفاتورة
                var invoice = await _salesInvoiceRepository.GetByIdAsync(invoiceId);
                if (invoice == null)
                {
                    throw new InvalidOperationException($"فاتورة البيع برقم {invoiceId} غير موجودة");
                }

                // التحقق من حالة الفاتورة
                if (invoice.Status == "Posted")
                {
                    throw new InvalidOperationException("الفاتورة مرحلة مسبقاً");
                }

                if (invoice.Status == "Cancelled")
                {
                    throw new InvalidOperationException("لا يمكن ترحيل فاتورة ملغاة");
                }

                // TODO: تحديث المخزون وأرصدة العملاء

                var success = await _salesInvoiceRepository.PostInvoiceAsync(invoiceId, userId);
                if (success)
                {
                    _logger.LogInformation("تم ترحيل فاتورة البيع برقم {InvoiceId} بنجاح", invoiceId);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في ترحيل فاتورة البيع برقم {InvoiceId}", invoiceId);
                throw;
            }
        }

        public async Task<string> GenerateSalesInvoiceNumberAsync()
        {
            try
            {
                return await _salesInvoiceRepository.GetNextInvoiceNumberAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد رقم فاتورة البيع");
                throw;
            }
        }

        public async Task<IEnumerable<SalesInvoice>> SearchSalesInvoicesAsync(string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return await GetAllSalesInvoicesAsync();

                return await _salesInvoiceRepository.SearchAsync(searchTerm);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن فواتير البيع بالمصطلح {SearchTerm}", searchTerm);
                throw;
            }
        }

        public async Task<IEnumerable<SalesInvoiceDetail>> GetSalesInvoiceDetailsAsync(int invoiceId)
        {
            try
            {
                if (invoiceId <= 0)
                    throw new ArgumentException("رقم الفاتورة غير صحيح", nameof(invoiceId));

                return await _salesInvoiceRepository.GetInvoiceDetailsAsync(invoiceId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب تفاصيل فاتورة البيع {InvoiceId}", invoiceId);
                throw;
            }
        }

        public async Task<IEnumerable<Customer>> GetActiveCustomersAsync()
        {
            try
            {
                return await _customerRepository.GetActiveCustomersAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب العملاء النشطين");
                throw;
            }
        }

        public async Task<IEnumerable<Warehouse>> GetActiveWarehousesAsync()
        {
            try
            {
                return await _warehouseRepository.GetActiveWarehousesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المخازن النشطة");
                throw;
            }
        }

        public async Task<IEnumerable<Item>> GetActiveItemsAsync()
        {
            try
            {
                return await _itemRepository.GetActiveItemsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الأصناف النشطة");
                throw;
            }
        }

        private async Task ValidateSalesInvoiceAsync(SalesInvoice invoice)
        {
            if (invoice == null)
                throw new ArgumentNullException(nameof(invoice));

            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(invoice);

            if (!Validator.TryValidateObject(invoice, validationContext, validationResults, true))
            {
                var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
                throw new ValidationException($"بيانات فاتورة البيع غير صحيحة: {errors}");
            }

            // تحققات إضافية
            if (invoice.InvoiceDate > DateTime.Today)
                throw new ValidationException("تاريخ الفاتورة لا يمكن أن يكون في المستقبل");

            if (invoice.TotalAmount < 0)
                throw new ValidationException("إجمالي الفاتورة لا يمكن أن يكون سالباً");

            if (invoice.PaidAmount < 0)
                throw new ValidationException("المبلغ المدفوع لا يمكن أن يكون سالباً");

            if (invoice.PaidAmount > invoice.TotalAmount)
                throw new ValidationException("المبلغ المدفوع لا يمكن أن يكون أكبر من إجمالي الفاتورة");

            // التحقق من وجود العميل
            var customer = await _customerRepository.GetByIdAsync(invoice.CustomerId);
            if (customer == null)
            {
                throw new ValidationException("العميل غير موجود");
            }

            // التحقق من وجود المخزن
            var warehouse = await _warehouseRepository.GetByIdAsync(invoice.WarehouseId);
            if (warehouse == null)
            {
                throw new ValidationException("المخزن غير موجود");
            }
        }
    }
}
