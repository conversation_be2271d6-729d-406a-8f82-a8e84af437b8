using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using POSSystem.Services;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace POSSystem.Forms
{
    public partial class MainForm : Form
    {
        private readonly ILogger<MainForm> _logger;
        private readonly IServiceProvider _serviceProvider;

        // Controls
        private MenuStrip mainMenuStrip;
        private ToolStrip mainToolStrip;
        private StatusStrip statusStrip;
        private Panel contentPanel;
        private Label welcomeLabel;

        // Menu items
        private ToolStripMenuItem masterDataMenu;
        private ToolStripMenuItem salesMenu;
        private ToolStripMenuItem purchaseMenu;
        private ToolStripMenuItem financialMenu;
        private ToolStripMenuItem warehouseMenu;
        private ToolStripMenuItem reportsMenu;
        private ToolStripMenuItem settingsMenu;
        private ToolStripMenuItem helpMenu;

        public MainForm(ILogger<MainForm> logger, IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "نظام إدارة المبيعات والمشتريات";
            this.WindowState = FormWindowState.Maximized;
            this.IsMdiContainer = true;
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Create menu strip
            CreateMenuStrip();

            // Create toolbar
            CreateToolStrip();

            // Create status strip
            CreateStatusStrip();

            // Create content panel
            CreateContentPanel();

            // Event handlers
            this.Load += MainForm_Load;
            this.FormClosing += MainForm_FormClosing;

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void CreateMenuStrip()
        {
            mainMenuStrip = new MenuStrip
            {
                Font = new Font("Tahoma", 10),
                BackColor = Color.FromArgb(248, 249, 250),
                RightToLeft = RightToLeft.Yes
            };

            // البيانات الأساسية
            masterDataMenu = new ToolStripMenuItem("البيانات الأساسية");
            masterDataMenu.DropDownItems.Add("العملاء", null, (s, e) => OpenCustomerForm());
            masterDataMenu.DropDownItems.Add("الموردين", null, (s, e) => OpenSupplierForm());
            masterDataMenu.DropDownItems.Add("المناديب", null, (s, e) => OpenSalesRepForm());
            masterDataMenu.DropDownItems.Add("-");
            masterDataMenu.DropDownItems.Add("الأصناف", null, (s, e) => OpenItemForm());
            masterDataMenu.DropDownItems.Add("مجموعات الأصناف", null, (s, e) => OpenItemCategoryForm());
            masterDataMenu.DropDownItems.Add("الوحدات", null, (s, e) => OpenUnitForm());
            masterDataMenu.DropDownItems.Add("-");
            masterDataMenu.DropDownItems.Add("المخازن", null, (s, e) => OpenWarehouseForm());
            masterDataMenu.DropDownItems.Add("الخزائن", null, (s, e) => OpenTreasuryForm());

            // المبيعات
            salesMenu = new ToolStripMenuItem("المبيعات");
            salesMenu.DropDownItems.Add("عروض الأسعار", null, (s, e) => OpenQuotationForm());
            salesMenu.DropDownItems.Add("فواتير البيع", null, (s, e) => OpenSalesInvoiceForm());
            salesMenu.DropDownItems.Add("مرتجعات البيع", null, (s, e) => OpenSalesReturnForm());

            // المشتريات
            purchaseMenu = new ToolStripMenuItem("المشتريات");
            purchaseMenu.DropDownItems.Add("طلبيات الشراء", null, (s, e) => OpenPurchaseOrderForm());
            purchaseMenu.DropDownItems.Add("فواتير الشراء", null, (s, e) => OpenPurchaseInvoiceForm());
            purchaseMenu.DropDownItems.Add("مرتجعات الشراء", null, (s, e) => OpenPurchaseReturnForm());

            // المالية
            financialMenu = new ToolStripMenuItem("المالية");
            financialMenu.DropDownItems.Add("سندات القبض", null, (s, e) => OpenReceiptForm());
            financialMenu.DropDownItems.Add("سندات الصرف", null, (s, e) => OpenPaymentForm());
            financialMenu.DropDownItems.Add("التحويل بين الخزائن", null, (s, e) => OpenTreasuryTransferForm());

            // المخازن
            warehouseMenu = new ToolStripMenuItem("المخازن");
            warehouseMenu.DropDownItems.Add("جرد المخازن", null, (s, e) => OpenStockCountForm());
            warehouseMenu.DropDownItems.Add("التحويل بين المخازن", null, (s, e) => OpenWarehouseTransferForm());
            warehouseMenu.DropDownItems.Add("حركة الأصناف", null, (s, e) => OpenStockMovementForm());

            // التقارير
            reportsMenu = new ToolStripMenuItem("التقارير");
            reportsMenu.DropDownItems.Add("كشف حساب عميل", null, (s, e) => OpenCustomerStatementReport());
            reportsMenu.DropDownItems.Add("كشف حساب مورد", null, (s, e) => OpenSupplierStatementReport());
            reportsMenu.DropDownItems.Add("كشف حساب مندوب", null, (s, e) => OpenSalesRepStatementReport());
            reportsMenu.DropDownItems.Add("-");
            reportsMenu.DropDownItems.Add("تقرير المبيعات", null, (s, e) => OpenSalesReport());
            reportsMenu.DropDownItems.Add("تقرير المشتريات", null, (s, e) => OpenPurchaseReport());
            reportsMenu.DropDownItems.Add("تقرير المخزون", null, (s, e) => OpenStockReport());
            reportsMenu.DropDownItems.Add("-");
            reportsMenu.DropDownItems.Add("الأرباح والخسائر", null, (s, e) => OpenProfitLossReport());
            reportsMenu.DropDownItems.Add("المركز المالي", null, (s, e) => OpenBalanceSheetReport());
            reportsMenu.DropDownItems.Add("ميزان المراجعة", null, (s, e) => OpenTrialBalanceReport());

            // الإعدادات
            settingsMenu = new ToolStripMenuItem("الإعدادات");
            settingsMenu.DropDownItems.Add("المستخدمين", null, (s, e) => OpenUserForm());
            settingsMenu.DropDownItems.Add("الصلاحيات", null, (s, e) => OpenPermissionForm());
            settingsMenu.DropDownItems.Add("إعدادات النظام", null, (s, e) => OpenSystemSettingsForm());
            settingsMenu.DropDownItems.Add("-");
            settingsMenu.DropDownItems.Add("نسخ احتياطي", null, (s, e) => CreateBackup());
            settingsMenu.DropDownItems.Add("استعادة نسخة احتياطية", null, (s, e) => RestoreBackup());

            // المساعدة
            helpMenu = new ToolStripMenuItem("المساعدة");
            helpMenu.DropDownItems.Add("دليل المستخدم", null, (s, e) => OpenUserGuide());
            helpMenu.DropDownItems.Add("حول البرنامج", null, (s, e) => ShowAbout());
            helpMenu.DropDownItems.Add("-");
            helpMenu.DropDownItems.Add("تسجيل الخروج", null, (s, e) => Logout());

            mainMenuStrip.Items.Add(masterDataMenu);
            mainMenuStrip.Items.Add(salesMenu);
            mainMenuStrip.Items.Add(purchaseMenu);
            mainMenuStrip.Items.Add(financialMenu);
            mainMenuStrip.Items.Add(warehouseMenu);
            mainMenuStrip.Items.Add(reportsMenu);
            mainMenuStrip.Items.Add(settingsMenu);
            mainMenuStrip.Items.Add(helpMenu);

            this.MainMenuStrip = mainMenuStrip;
            this.Controls.Add(mainMenuStrip);
        }

        private void CreateToolStrip()
        {
            mainToolStrip = new ToolStrip
            {
                Font = new Font("Tahoma", 9),
                BackColor = Color.FromArgb(248, 249, 250),
                RightToLeft = RightToLeft.Yes
            };

            // Add common toolbar buttons
            mainToolStrip.Items.Add(new ToolStripButton("العملاء", null, (s, e) => OpenCustomerForm()));
            mainToolStrip.Items.Add(new ToolStripButton("الأصناف", null, (s, e) => OpenItemForm()));
            mainToolStrip.Items.Add(new ToolStripSeparator());
            mainToolStrip.Items.Add(new ToolStripButton("فاتورة بيع", null, (s, e) => OpenSalesInvoiceForm()));
            mainToolStrip.Items.Add(new ToolStripButton("فاتورة شراء", null, (s, e) => OpenPurchaseInvoiceForm()));
            mainToolStrip.Items.Add(new ToolStripSeparator());
            mainToolStrip.Items.Add(new ToolStripButton("سند قبض", null, (s, e) => OpenReceiptForm()));
            mainToolStrip.Items.Add(new ToolStripButton("سند صرف", null, (s, e) => OpenPaymentForm()));

            this.Controls.Add(mainToolStrip);
        }

        private void CreateStatusStrip()
        {
            statusStrip = new StatusStrip
            {
                Font = new Font("Tahoma", 9),
                BackColor = Color.FromArgb(248, 249, 250),
                RightToLeft = RightToLeft.Yes
            };

            var userLabel = new ToolStripStatusLabel($"المستخدم: {CurrentUser.User?.FullName ?? "غير محدد"}");
            var dateLabel = new ToolStripStatusLabel($"التاريخ: {DateTime.Now:yyyy/MM/dd}");
            var timeLabel = new ToolStripStatusLabel($"الوقت: {DateTime.Now:HH:mm:ss}");

            statusStrip.Items.Add(userLabel);
            statusStrip.Items.Add(new ToolStripStatusLabel("|"));
            statusStrip.Items.Add(dateLabel);
            statusStrip.Items.Add(new ToolStripStatusLabel("|"));
            statusStrip.Items.Add(timeLabel);

            // Update time every second
            var timer = new Timer { Interval = 1000 };
            timer.Tick += (s, e) => timeLabel.Text = $"الوقت: {DateTime.Now:HH:mm:ss}";
            timer.Start();

            this.Controls.Add(statusStrip);
        }

        private void CreateContentPanel()
        {
            contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(240, 240, 240)
            };

            welcomeLabel = new Label
            {
                Text = $"مرحباً {CurrentUser.User?.FullName ?? ""}!\nأهلاً بك في نظام إدارة المبيعات والمشتريات",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(51, 51, 51),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };

            contentPanel.Controls.Add(welcomeLabel);
            this.Controls.Add(contentPanel);
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            try
            {
                _logger.LogInformation("تم فتح النموذج الرئيسي للمستخدم {Username}", CurrentUser.User?.Username);
                
                // TODO: Load user permissions and enable/disable menu items accordingly
                // LoadUserPermissions();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل النموذج الرئيسي");
                MessageBox.Show("خطأ في تحميل النموذج الرئيسي", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (MessageBox.Show("هل تريد إغلاق البرنامج؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No)
            {
                e.Cancel = true;
            }
        }

        // Form opening methods (placeholders)
        private void OpenCustomerForm() => ShowMessage("سيتم فتح نموذج العملاء");
        private void OpenSupplierForm() => ShowMessage("سيتم فتح نموذج الموردين");
        private void OpenSalesRepForm() => ShowMessage("سيتم فتح نموذج المناديب");
        private void OpenItemForm() => ShowMessage("سيتم فتح نموذج الأصناف");
        private void OpenItemCategoryForm() => ShowMessage("سيتم فتح نموذج مجموعات الأصناف");
        private void OpenUnitForm() => ShowMessage("سيتم فتح نموذج الوحدات");
        private void OpenWarehouseForm() => ShowMessage("سيتم فتح نموذج المخازن");
        private void OpenTreasuryForm() => ShowMessage("سيتم فتح نموذج الخزائن");
        private void OpenQuotationForm() => ShowMessage("سيتم فتح نموذج عروض الأسعار");
        private void OpenSalesInvoiceForm() => ShowMessage("سيتم فتح نموذج فواتير البيع");
        private void OpenSalesReturnForm() => ShowMessage("سيتم فتح نموذج مرتجعات البيع");
        private void OpenPurchaseOrderForm() => ShowMessage("سيتم فتح نموذج طلبيات الشراء");
        private void OpenPurchaseInvoiceForm() => ShowMessage("سيتم فتح نموذج فواتير الشراء");
        private void OpenPurchaseReturnForm() => ShowMessage("سيتم فتح نموذج مرتجعات الشراء");
        private void OpenReceiptForm() => ShowMessage("سيتم فتح نموذج سندات القبض");
        private void OpenPaymentForm() => ShowMessage("سيتم فتح نموذج سندات الصرف");
        private void OpenTreasuryTransferForm() => ShowMessage("سيتم فتح نموذج التحويل بين الخزائن");
        private void OpenStockCountForm() => ShowMessage("سيتم فتح نموذج جرد المخازن");
        private void OpenWarehouseTransferForm() => ShowMessage("سيتم فتح نموذج التحويل بين المخازن");
        private void OpenStockMovementForm() => ShowMessage("سيتم فتح نموذج حركة الأصناف");
        private void OpenCustomerStatementReport() => ShowMessage("سيتم فتح تقرير كشف حساب عميل");
        private void OpenSupplierStatementReport() => ShowMessage("سيتم فتح تقرير كشف حساب مورد");
        private void OpenSalesRepStatementReport() => ShowMessage("سيتم فتح تقرير كشف حساب مندوب");
        private void OpenSalesReport() => ShowMessage("سيتم فتح تقرير المبيعات");
        private void OpenPurchaseReport() => ShowMessage("سيتم فتح تقرير المشتريات");
        private void OpenStockReport() => ShowMessage("سيتم فتح تقرير المخزون");
        private void OpenProfitLossReport() => ShowMessage("سيتم فتح تقرير الأرباح والخسائر");
        private void OpenBalanceSheetReport() => ShowMessage("سيتم فتح تقرير المركز المالي");
        private void OpenTrialBalanceReport() => ShowMessage("سيتم فتح تقرير ميزان المراجعة");
        private void OpenUserForm() => ShowMessage("سيتم فتح نموذج المستخدمين");
        private void OpenPermissionForm() => ShowMessage("سيتم فتح نموذج الصلاحيات");
        private void OpenSystemSettingsForm() => ShowMessage("سيتم فتح نموذج إعدادات النظام");
        private void CreateBackup() => ShowMessage("سيتم إنشاء نسخة احتياطية");
        private void RestoreBackup() => ShowMessage("سيتم استعادة نسخة احتياطية");
        private void OpenUserGuide() => ShowMessage("سيتم فتح دليل المستخدم");
        private void ShowAbout() => MessageBox.Show("نظام إدارة المبيعات والمشتريات\nالإصدار 1.0\n\nتم التطوير بواسطة فريق التطوير", "حول البرنامج", MessageBoxButtons.OK, MessageBoxIcon.Information);

        private void Logout()
        {
            if (MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                CurrentUser.User = null;
                this.Hide();
                var loginForm = _serviceProvider.GetRequiredService<LoginForm>();
                loginForm.ShowDialog();
                this.Close();
            }
        }

        private void ShowMessage(string message)
        {
            MessageBox.Show(message, "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
