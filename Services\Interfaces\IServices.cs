using POSSystem.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace POSSystem.Services
{
    public interface ICustomerService
    {
        Task<IEnumerable<Customer>> GetAllCustomersAsync();
        Task<Customer?> GetCustomerByIdAsync(int id);
        Task<Customer?> GetCustomerByCodeAsync(string customerCode);
        Task<int> AddCustomerAsync(Customer customer);
        Task<bool> UpdateCustomerAsync(Customer customer);
        Task<bool> DeleteCustomerAsync(int id);
        Task<bool> IsCustomerCodeExistsAsync(string customerCode, int? excludeId = null);
        Task<IEnumerable<Customer>> SearchCustomersAsync(string searchTerm);
        Task<IEnumerable<Customer>> GetActiveCustomersAsync();
        Task<decimal> GetCustomerBalanceAsync(int customerId);
        Task<bool> UpdateCustomerBalanceAsync(int customerId, decimal amount);
        Task<string> GenerateCustomerCodeAsync();
    }

    public interface ISupplierService
    {
        Task<IEnumerable<Supplier>> GetAllSuppliersAsync();
        Task<Supplier?> GetSupplierByIdAsync(int id);
        Task<Supplier?> GetSupplierByCodeAsync(string supplierCode);
        Task<int> AddSupplierAsync(Supplier supplier);
        Task<bool> UpdateSupplierAsync(Supplier supplier);
        Task<bool> DeleteSupplierAsync(int id);
        Task<bool> IsSupplierCodeExistsAsync(string supplierCode, int? excludeId = null);
        Task<IEnumerable<Supplier>> SearchSuppliersAsync(string searchTerm);
        Task<IEnumerable<Supplier>> GetActiveSuppliersAsync();
        Task<decimal> GetSupplierBalanceAsync(int supplierId);
        Task<bool> UpdateSupplierBalanceAsync(int supplierId, decimal amount);
        Task<string> GenerateSupplierCodeAsync();
    }

    public interface ISalesRepService
    {
        Task<IEnumerable<SalesRep>> GetAllSalesRepsAsync();
        Task<SalesRep?> GetSalesRepByIdAsync(int id);
        Task<SalesRep?> GetSalesRepByCodeAsync(string salesRepCode);
        Task<int> AddSalesRepAsync(SalesRep salesRep);
        Task<bool> UpdateSalesRepAsync(SalesRep salesRep);
        Task<bool> DeleteSalesRepAsync(int id);
        Task<bool> IsSalesRepCodeExistsAsync(string salesRepCode, int? excludeId = null);
        Task<IEnumerable<SalesRep>> SearchSalesRepsAsync(string searchTerm);
        Task<IEnumerable<SalesRep>> GetActiveSalesRepsAsync();
        Task<decimal> GetSalesRepBalanceAsync(int salesRepId);
        Task<bool> UpdateSalesRepBalanceAsync(int salesRepId, decimal amount);
        Task<string> GenerateSalesRepCodeAsync();
    }

    public interface IItemService
    {
        Task<IEnumerable<Item>> GetAllItemsAsync();
        Task<Item?> GetItemByIdAsync(int id);
        Task<Item?> GetItemByCodeAsync(string itemCode);
        Task<Item?> GetItemByBarcodeAsync(string barcode);
        Task<int> AddItemAsync(Item item);
        Task<bool> UpdateItemAsync(Item item);
        Task<bool> DeleteItemAsync(int id);
        Task<bool> IsItemCodeExistsAsync(string itemCode, int? excludeId = null);
        Task<bool> IsBarcodeExistsAsync(string barcode, int? excludeId = null);
        Task<IEnumerable<Item>> SearchItemsAsync(string searchTerm);
        Task<IEnumerable<Item>> GetActiveItemsAsync();
        Task<IEnumerable<Item>> GetItemsByCategoryAsync(int categoryId);
        Task<IEnumerable<ItemUnit>> GetItemUnitsAsync(int itemId);
        Task<ItemStock?> GetItemStockAsync(int itemId, int warehouseId, int unitId);
        Task<bool> UpdateItemStockAsync(int itemId, int warehouseId, int unitId, decimal quantity, decimal cost);
        Task<string> GenerateItemCodeAsync();
        Task<string> GenerateBarcodeAsync();
        Task<IEnumerable<Unit>> GetAllUnitsAsync();
        Task<IEnumerable<ItemCategory>> GetAllCategoriesAsync();
    }

    public interface IWarehouseService
    {
        Task<IEnumerable<Warehouse>> GetAllWarehousesAsync();
        Task<Warehouse?> GetWarehouseByIdAsync(int id);
        Task<Warehouse?> GetWarehouseByCodeAsync(string warehouseCode);
        Task<int> AddWarehouseAsync(Warehouse warehouse);
        Task<bool> UpdateWarehouseAsync(Warehouse warehouse);
        Task<bool> DeleteWarehouseAsync(int id);
        Task<bool> IsWarehouseCodeExistsAsync(string warehouseCode, int? excludeId = null);
        Task<IEnumerable<Warehouse>> SearchWarehousesAsync(string searchTerm);
        Task<IEnumerable<Warehouse>> GetActiveWarehousesAsync();
        Task<string> GenerateWarehouseCodeAsync();
    }

    public interface ITreasuryService
    {
        Task<IEnumerable<Treasury>> GetAllTreasuriesAsync();
        Task<Treasury?> GetTreasuryByIdAsync(int id);
        Task<Treasury?> GetTreasuryByCodeAsync(string treasuryCode);
        Task<int> AddTreasuryAsync(Treasury treasury);
        Task<bool> UpdateTreasuryAsync(Treasury treasury);
        Task<bool> DeleteTreasuryAsync(int id);
        Task<bool> IsTreasuryCodeExistsAsync(string treasuryCode, int? excludeId = null);
        Task<IEnumerable<Treasury>> SearchTreasuriesAsync(string searchTerm);
        Task<IEnumerable<Treasury>> GetActiveTreasuriesAsync();
        Task<decimal> GetTreasuryBalanceAsync(int treasuryId);
        Task<bool> UpdateTreasuryBalanceAsync(int treasuryId, decimal amount);
        Task<string> GenerateTreasuryCodeAsync();
    }

    public interface ISalesService
    {
        Task<IEnumerable<SalesInvoice>> GetAllSalesInvoicesAsync();
        Task<SalesInvoice?> GetSalesInvoiceByIdAsync(int id);
        Task<SalesInvoice?> GetSalesInvoiceByNumberAsync(string invoiceNumber);
        Task<int> AddSalesInvoiceAsync(SalesInvoice invoice);
        Task<bool> UpdateSalesInvoiceAsync(SalesInvoice invoice);
        Task<bool> DeleteSalesInvoiceAsync(int id);
        Task<bool> PostSalesInvoiceAsync(int invoiceId, int userId);
        Task<string> GenerateSalesInvoiceNumberAsync();
        Task<IEnumerable<SalesInvoice>> SearchSalesInvoicesAsync(string searchTerm);
        Task<IEnumerable<SalesInvoiceDetail>> GetSalesInvoiceDetailsAsync(int invoiceId);
        Task<IEnumerable<Customer>> GetActiveCustomersAsync();
        Task<IEnumerable<Warehouse>> GetActiveWarehousesAsync();
        Task<IEnumerable<Item>> GetActiveItemsAsync();
    }

    public interface IPurchaseService
    {
        Task<IEnumerable<PurchaseOrder>> GetAllPurchaseOrdersAsync();
        Task<PurchaseOrder?> GetPurchaseOrderByIdAsync(int id);
        Task<PurchaseOrder?> GetPurchaseOrderByNumberAsync(string orderNumber);
        Task<int> AddPurchaseOrderAsync(PurchaseOrder order);
        Task<bool> UpdatePurchaseOrderAsync(PurchaseOrder order);
        Task<bool> DeletePurchaseOrderAsync(int id);
        Task<string> GeneratePurchaseOrderNumberAsync();

        Task<IEnumerable<PurchaseInvoice>> GetAllPurchaseInvoicesAsync();
        Task<PurchaseInvoice?> GetPurchaseInvoiceByIdAsync(int id);
        Task<PurchaseInvoice?> GetPurchaseInvoiceByNumberAsync(string invoiceNumber);
        Task<int> AddPurchaseInvoiceAsync(PurchaseInvoice invoice);
        Task<bool> UpdatePurchaseInvoiceAsync(PurchaseInvoice invoice);
        Task<bool> DeletePurchaseInvoiceAsync(int id);
        Task<bool> PostPurchaseInvoiceAsync(int invoiceId, int userId);
        Task<string> GeneratePurchaseInvoiceNumberAsync();

        Task<IEnumerable<PurchaseReturn>> GetAllPurchaseReturnsAsync();
        Task<PurchaseReturn?> GetPurchaseReturnByIdAsync(int id);
        Task<int> AddPurchaseReturnAsync(PurchaseReturn purchaseReturn);
        Task<bool> UpdatePurchaseReturnAsync(PurchaseReturn purchaseReturn);
        Task<bool> PostPurchaseReturnAsync(int returnId, int userId);
        Task<string> GeneratePurchaseReturnNumberAsync();
    }

    public interface IFinancialService
    {
        Task<IEnumerable<FinancialTransaction>> GetAllTransactionsAsync();
        Task<FinancialTransaction?> GetTransactionByIdAsync(int id);
        Task<FinancialTransaction?> GetTransactionByNumberAsync(string transactionNumber);
        Task<int> AddTransactionAsync(FinancialTransaction transaction);
        Task<bool> UpdateTransactionAsync(FinancialTransaction transaction);
        Task<bool> DeleteTransactionAsync(int id);
        Task<bool> PostTransactionAsync(int transactionId, int userId);
        Task<string> GenerateTransactionNumberAsync(string transactionType);

        Task<IEnumerable<TreasuryTransfer>> GetAllTreasuryTransfersAsync();
        Task<TreasuryTransfer?> GetTreasuryTransferByIdAsync(int id);
        Task<int> AddTreasuryTransferAsync(TreasuryTransfer transfer);
        Task<bool> UpdateTreasuryTransferAsync(TreasuryTransfer transfer);
        Task<bool> PostTreasuryTransferAsync(int transferId, int userId);
        Task<string> GenerateTreasuryTransferNumberAsync();

        Task<IEnumerable<WarehouseTransfer>> GetAllWarehouseTransfersAsync();
        Task<WarehouseTransfer?> GetWarehouseTransferByIdAsync(int id);
        Task<int> AddWarehouseTransferAsync(WarehouseTransfer transfer);
        Task<bool> UpdateWarehouseTransferAsync(WarehouseTransfer transfer);
        Task<bool> PostWarehouseTransferAsync(int transferId, int userId);
        Task<string> GenerateWarehouseTransferNumberAsync();

        Task<IEnumerable<StockCount>> GetAllStockCountsAsync();
        Task<StockCount?> GetStockCountByIdAsync(int id);
        Task<int> AddStockCountAsync(StockCount stockCount);
        Task<bool> UpdateStockCountAsync(StockCount stockCount);
        Task<bool> PostStockCountAsync(int stockCountId, int userId);
        Task<string> GenerateStockCountNumberAsync();
    }

    public interface IUserService
    {
        Task<IEnumerable<User>> GetAllUsersAsync();
        Task<User?> GetUserByIdAsync(int id);
        Task<User?> GetUserByUsernameAsync(string username);
        Task<int> AddUserAsync(User user);
        Task<bool> UpdateUserAsync(User user);
        Task<bool> DeleteUserAsync(int id);
        Task<bool> IsUsernameExistsAsync(string username, int? excludeId = null);
        Task<bool> ValidatePasswordAsync(string username, string password);
        Task<bool> UpdatePasswordAsync(int userId, string newPassword);
        Task<bool> UpdateLastLoginAsync(int userId);
        Task<IEnumerable<UserPermission>> GetUserPermissionsAsync(int userId);
        Task<bool> UpdateUserPermissionsAsync(int userId, IEnumerable<UserPermission> permissions);
        string HashPassword(string password);
        bool VerifyPassword(string password, string hashedPassword);
    }

    public interface IReportService
    {
        Task<DataTable> GetCustomerStatementAsync(int customerId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<DataTable> GetSupplierStatementAsync(int supplierId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<DataTable> GetSalesRepStatementAsync(int salesRepId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<DataTable> GetItemMovementReportAsync(int itemId, int? warehouseId = null, DateTime? fromDate = null, DateTime? toDate = null);
        Task<DataTable> GetStockReportAsync(int? warehouseId = null, int? categoryId = null);
        Task<DataTable> GetSalesReportAsync(DateTime fromDate, DateTime toDate, int? customerId = null, int? salesRepId = null);
        Task<DataTable> GetPurchaseReportAsync(DateTime fromDate, DateTime toDate, int? supplierId = null);
        Task<DataTable> GetProfitLossReportAsync(DateTime fromDate, DateTime toDate);
        Task<DataTable> GetBalanceSheetAsync(DateTime asOfDate);
        Task<DataTable> GetTrialBalanceAsync(DateTime fromDate, DateTime toDate);
        Task<DataTable> GetTopSellingItemsAsync(DateTime fromDate, DateTime toDate, int topCount = 10);
        Task<DataTable> GetSlowMovingItemsAsync(int? warehouseId = null, int daysThreshold = 90);
        Task<DataTable> GetItemsBelowMinimumAsync(int? warehouseId = null);
        Task<DataTable> GetExpiringItemsAsync(int? warehouseId = null, int daysThreshold = 30);
    }
}
