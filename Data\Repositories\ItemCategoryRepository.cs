using Microsoft.Extensions.Logging;
using POSSystem.Data.Interfaces;
using POSSystem.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;

namespace POSSystem.Data.Repositories
{
    public class ItemCategoryRepository : IItemCategoryRepository
    {
        private readonly DatabaseManager _dbManager;
        private readonly ILogger<ItemCategoryRepository> _logger;

        public ItemCategoryRepository(DatabaseManager dbManager, ILogger<ItemCategoryRepository> logger)
        {
            _dbManager = dbManager;
            _logger = logger;
        }

        public async Task<IEnumerable<ItemCategory>> GetAllAsync()
        {
            try
            {
                var query = @"
                    SELECT CategoryID, CategoryCode, CategoryName, ParentCategoryID, IsActive
                    FROM ItemCategories 
                    ORDER BY CategoryName";

                var dataTable = await _dbManager.ExecuteQueryAsync(query);
                return MapDataTableToCategories(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب جميع مجموعات الأصناف");
                throw;
            }
        }

        public async Task<ItemCategory?> GetByIdAsync(int id)
        {
            try
            {
                var query = @"
                    SELECT CategoryID, CategoryCode, CategoryName, ParentCategoryID, IsActive
                    FROM ItemCategories 
                    WHERE CategoryID = @CategoryID";

                var parameters = new[] { new SqlParameter("@CategoryID", id) };
                var dataTable = await _dbManager.ExecuteQueryAsync(query, parameters);

                if (dataTable.Rows.Count == 0)
                    return null;

                return MapDataRowToCategory(dataTable.Rows[0]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب مجموعة الأصناف برقم {CategoryId}", id);
                throw;
            }
        }

        public async Task<ItemCategory?> GetByCodeAsync(string categoryCode)
        {
            try
            {
                var query = @"
                    SELECT CategoryID, CategoryCode, CategoryName, ParentCategoryID, IsActive
                    FROM ItemCategories 
                    WHERE CategoryCode = @CategoryCode";

                var parameters = new[] { new SqlParameter("@CategoryCode", categoryCode) };
                var dataTable = await _dbManager.ExecuteQueryAsync(query, parameters);

                if (dataTable.Rows.Count == 0)
                    return null;

                return MapDataRowToCategory(dataTable.Rows[0]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب مجموعة الأصناف بالكود {CategoryCode}", categoryCode);
                throw;
            }
        }

        public async Task<int> AddAsync(ItemCategory entity)
        {
            try
            {
                var query = @"
                    INSERT INTO ItemCategories (CategoryCode, CategoryName, ParentCategoryID, IsActive)
                    VALUES (@CategoryCode, @CategoryName, @ParentCategoryID, @IsActive);
                    SELECT SCOPE_IDENTITY();";

                var parameters = new[]
                {
                    new SqlParameter("@CategoryCode", entity.CategoryCode),
                    new SqlParameter("@CategoryName", entity.CategoryName),
                    new SqlParameter("@ParentCategoryID", (object?)entity.ParentCategoryId ?? DBNull.Value),
                    new SqlParameter("@IsActive", entity.IsActive)
                };

                var result = await _dbManager.ExecuteScalarAsync(query, parameters);
                var categoryId = Convert.ToInt32(result);

                _logger.LogInformation("تم إضافة مجموعة الأصناف {CategoryName} بنجاح برقم {CategoryId}", entity.CategoryName, categoryId);
                return categoryId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة مجموعة الأصناف {CategoryName}", entity.CategoryName);
                throw;
            }
        }

        public async Task<bool> UpdateAsync(ItemCategory entity)
        {
            try
            {
                var query = @"
                    UPDATE ItemCategories 
                    SET CategoryCode = @CategoryCode, CategoryName = @CategoryName, 
                        ParentCategoryID = @ParentCategoryID, IsActive = @IsActive
                    WHERE CategoryID = @CategoryID";

                var parameters = new[]
                {
                    new SqlParameter("@CategoryID", entity.Id),
                    new SqlParameter("@CategoryCode", entity.CategoryCode),
                    new SqlParameter("@CategoryName", entity.CategoryName),
                    new SqlParameter("@ParentCategoryID", (object?)entity.ParentCategoryId ?? DBNull.Value),
                    new SqlParameter("@IsActive", entity.IsActive)
                };

                var rowsAffected = await _dbManager.ExecuteNonQueryAsync(query, parameters);
                var success = rowsAffected > 0;

                if (success)
                    _logger.LogInformation("تم تحديث مجموعة الأصناف {CategoryName} بنجاح", entity.CategoryName);

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث مجموعة الأصناف {CategoryName}", entity.CategoryName);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                var query = "UPDATE ItemCategories SET IsActive = 0 WHERE CategoryID = @CategoryID";
                var parameters = new[] { new SqlParameter("@CategoryID", id) };

                var rowsAffected = await _dbManager.ExecuteNonQueryAsync(query, parameters);
                var success = rowsAffected > 0;

                if (success)
                    _logger.LogInformation("تم حذف مجموعة الأصناف برقم {CategoryId} بنجاح", id);

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف مجموعة الأصناف برقم {CategoryId}", id);
                throw;
            }
        }

        public async Task<bool> ExistsAsync(int id)
        {
            try
            {
                var query = "SELECT COUNT(*) FROM ItemCategories WHERE CategoryID = @CategoryID";
                var parameters = new[] { new SqlParameter("@CategoryID", id) };

                var result = await _dbManager.ExecuteScalarAsync(query, parameters);
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من وجود مجموعة الأصناف برقم {CategoryId}", id);
                throw;
            }
        }

        public async Task<bool> IsCodeExistsAsync(string categoryCode, int? excludeId = null)
        {
            try
            {
                var query = "SELECT COUNT(*) FROM ItemCategories WHERE CategoryCode = @CategoryCode";
                var parameters = new List<SqlParameter> { new("@CategoryCode", categoryCode) };

                if (excludeId.HasValue)
                {
                    query += " AND CategoryID != @ExcludeId";
                    parameters.Add(new SqlParameter("@ExcludeId", excludeId.Value));
                }

                var result = await _dbManager.ExecuteScalarAsync(query, parameters.ToArray());
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من وجود كود مجموعة الأصناف {CategoryCode}", categoryCode);
                throw;
            }
        }

        public async Task<IEnumerable<ItemCategory>> GetActiveCategoriesAsync()
        {
            try
            {
                var query = @"
                    SELECT CategoryID, CategoryCode, CategoryName, ParentCategoryID, IsActive
                    FROM ItemCategories 
                    WHERE IsActive = 1
                    ORDER BY CategoryName";

                var dataTable = await _dbManager.ExecuteQueryAsync(query);
                return MapDataTableToCategories(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب مجموعات الأصناف النشطة");
                throw;
            }
        }

        public async Task<IEnumerable<ItemCategory>> GetMainCategoriesAsync()
        {
            try
            {
                var query = @"
                    SELECT CategoryID, CategoryCode, CategoryName, ParentCategoryID, IsActive
                    FROM ItemCategories 
                    WHERE ParentCategoryID IS NULL AND IsActive = 1
                    ORDER BY CategoryName";

                var dataTable = await _dbManager.ExecuteQueryAsync(query);
                return MapDataTableToCategories(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المجموعات الرئيسية");
                throw;
            }
        }

        public async Task<IEnumerable<ItemCategory>> GetSubCategoriesAsync(int parentCategoryId)
        {
            try
            {
                var query = @"
                    SELECT CategoryID, CategoryCode, CategoryName, ParentCategoryID, IsActive
                    FROM ItemCategories 
                    WHERE ParentCategoryID = @ParentCategoryID AND IsActive = 1
                    ORDER BY CategoryName";

                var parameters = new[] { new SqlParameter("@ParentCategoryID", parentCategoryId) };
                var dataTable = await _dbManager.ExecuteQueryAsync(query, parameters);
                return MapDataTableToCategories(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المجموعات الفرعية للمجموعة {ParentCategoryId}", parentCategoryId);
                throw;
            }
        }

        public async Task<IEnumerable<ItemCategory>> SearchAsync(string searchTerm)
        {
            try
            {
                var query = @"
                    SELECT CategoryID, CategoryCode, CategoryName, ParentCategoryID, IsActive
                    FROM ItemCategories 
                    WHERE CategoryCode LIKE @SearchTerm 
                       OR CategoryName LIKE @SearchTerm
                    ORDER BY CategoryName";

                var parameters = new[] { new SqlParameter("@SearchTerm", $"%{searchTerm}%") };
                var dataTable = await _dbManager.ExecuteQueryAsync(query, parameters);
                return MapDataTableToCategories(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن مجموعات الأصناف بالمصطلح {SearchTerm}", searchTerm);
                throw;
            }
        }

        private static IEnumerable<ItemCategory> MapDataTableToCategories(DataTable dataTable)
        {
            var categories = new List<ItemCategory>();
            foreach (DataRow row in dataTable.Rows)
            {
                categories.Add(MapDataRowToCategory(row));
            }
            return categories;
        }

        private static ItemCategory MapDataRowToCategory(DataRow row)
        {
            return new ItemCategory
            {
                Id = Convert.ToInt32(row["CategoryID"]),
                CategoryCode = row["CategoryCode"].ToString()!,
                CategoryName = row["CategoryName"].ToString()!,
                ParentCategoryId = row["ParentCategoryID"] as int?,
                IsActive = Convert.ToBoolean(row["IsActive"])
            };
        }
    }
}
