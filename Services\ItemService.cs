using Microsoft.Extensions.Logging;
using POSSystem.Data.Interfaces;
using POSSystem.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace POSSystem.Services
{
    public class ItemService : IItemService
    {
        private readonly IItemRepository _itemRepository;
        private readonly IUnitRepository _unitRepository;
        private readonly IItemCategoryRepository _categoryRepository;
        private readonly ILogger<ItemService> _logger;

        public ItemService(
            IItemRepository itemRepository,
            IUnitRepository unitRepository,
            IItemCategoryRepository categoryRepository,
            ILogger<ItemService> logger)
        {
            _itemRepository = itemRepository;
            _unitRepository = unitRepository;
            _categoryRepository = categoryRepository;
            _logger = logger;
        }

        public async Task<IEnumerable<Item>> GetAllItemsAsync()
        {
            try
            {
                return await _itemRepository.GetAllAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب جميع الأصناف");
                throw;
            }
        }

        public async Task<Item?> GetItemByIdAsync(int id)
        {
            try
            {
                if (id <= 0)
                    throw new ArgumentException("رقم الصنف غير صحيح", nameof(id));

                return await _itemRepository.GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الصنف برقم {ItemId}", id);
                throw;
            }
        }

        public async Task<Item?> GetItemByCodeAsync(string itemCode)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(itemCode))
                    throw new ArgumentException("كود الصنف مطلوب", nameof(itemCode));

                return await _itemRepository.GetByCodeAsync(itemCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الصنف بالكود {ItemCode}", itemCode);
                throw;
            }
        }

        public async Task<Item?> GetItemByBarcodeAsync(string barcode)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(barcode))
                    throw new ArgumentException("الباركود مطلوب", nameof(barcode));

                return await _itemRepository.GetByBarcodeAsync(barcode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الصنف بالباركود {Barcode}", barcode);
                throw;
            }
        }

        public async Task<int> AddItemAsync(Item item)
        {
            try
            {
                // التحقق من صحة البيانات
                await ValidateItemAsync(item);

                // التحقق من عدم تكرار الكود
                if (await _itemRepository.IsCodeExistsAsync(item.ItemCode))
                {
                    throw new InvalidOperationException($"كود الصنف '{item.ItemCode}' موجود مسبقاً");
                }

                // التحقق من عدم تكرار الباركود
                if (!string.IsNullOrEmpty(item.Barcode) && await _itemRepository.IsBarcodeExistsAsync(item.Barcode))
                {
                    throw new InvalidOperationException($"الباركود '{item.Barcode}' موجود مسبقاً");
                }

                // إعداد البيانات الافتراضية
                item.CreatedDate = DateTime.Now;
                item.IsActive = true;

                var itemId = await _itemRepository.AddAsync(item);
                _logger.LogInformation("تم إضافة الصنف {ItemName} بنجاح برقم {ItemId}", item.ItemName, itemId);

                return itemId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة الصنف {ItemName}", item.ItemName);
                throw;
            }
        }

        public async Task<bool> UpdateItemAsync(Item item)
        {
            try
            {
                // التحقق من صحة البيانات
                await ValidateItemAsync(item);

                // التحقق من وجود الصنف
                var existingItem = await _itemRepository.GetByIdAsync(item.Id);
                if (existingItem == null)
                {
                    throw new InvalidOperationException($"الصنف برقم {item.Id} غير موجود");
                }

                // التحقق من عدم تكرار الكود
                if (await _itemRepository.IsCodeExistsAsync(item.ItemCode, item.Id))
                {
                    throw new InvalidOperationException($"كود الصنف '{item.ItemCode}' موجود مسبقاً");
                }

                // التحقق من عدم تكرار الباركود
                if (!string.IsNullOrEmpty(item.Barcode) && await _itemRepository.IsBarcodeExistsAsync(item.Barcode, item.Id))
                {
                    throw new InvalidOperationException($"الباركود '{item.Barcode}' موجود مسبقاً");
                }

                // الحفاظ على البيانات الأساسية
                item.CreatedDate = existingItem.CreatedDate;
                item.CreatedBy = existingItem.CreatedBy;
                item.ModifiedDate = DateTime.Now;

                var success = await _itemRepository.UpdateAsync(item);
                if (success)
                {
                    _logger.LogInformation("تم تحديث الصنف {ItemName} بنجاح", item.ItemName);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الصنف {ItemName}", item.ItemName);
                throw;
            }
        }

        public async Task<bool> DeleteItemAsync(int id)
        {
            try
            {
                if (id <= 0)
                    throw new ArgumentException("رقم الصنف غير صحيح", nameof(id));

                // التحقق من وجود الصنف
                var item = await _itemRepository.GetByIdAsync(id);
                if (item == null)
                {
                    throw new InvalidOperationException($"الصنف برقم {id} غير موجود");
                }

                // TODO: التحقق من عدم وجود معاملات مرتبطة بالصنف

                var success = await _itemRepository.DeleteAsync(id);
                if (success)
                {
                    _logger.LogInformation("تم حذف الصنف {ItemName} بنجاح", item.ItemName);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الصنف برقم {ItemId}", id);
                throw;
            }
        }

        public async Task<bool> IsItemCodeExistsAsync(string itemCode, int? excludeId = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(itemCode))
                    return false;

                return await _itemRepository.IsCodeExistsAsync(itemCode, excludeId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من وجود كود الصنف {ItemCode}", itemCode);
                throw;
            }
        }

        public async Task<bool> IsBarcodeExistsAsync(string barcode, int? excludeId = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(barcode))
                    return false;

                return await _itemRepository.IsBarcodeExistsAsync(barcode, excludeId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من وجود الباركود {Barcode}", barcode);
                throw;
            }
        }

        public async Task<IEnumerable<Item>> SearchItemsAsync(string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return await GetAllItemsAsync();

                return await _itemRepository.SearchAsync(searchTerm);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن الأصناف بالمصطلح {SearchTerm}", searchTerm);
                throw;
            }
        }

        public async Task<IEnumerable<Item>> GetActiveItemsAsync()
        {
            try
            {
                return await _itemRepository.GetActiveItemsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الأصناف النشطة");
                throw;
            }
        }

        public async Task<IEnumerable<Item>> GetItemsByCategoryAsync(int categoryId)
        {
            try
            {
                if (categoryId <= 0)
                    throw new ArgumentException("رقم المجموعة غير صحيح", nameof(categoryId));

                return await _itemRepository.GetItemsByCategoryAsync(categoryId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب أصناف المجموعة {CategoryId}", categoryId);
                throw;
            }
        }

        public async Task<IEnumerable<ItemUnit>> GetItemUnitsAsync(int itemId)
        {
            try
            {
                if (itemId <= 0)
                    throw new ArgumentException("رقم الصنف غير صحيح", nameof(itemId));

                return await _itemRepository.GetItemUnitsAsync(itemId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب وحدات الصنف {ItemId}", itemId);
                throw;
            }
        }

        public async Task<ItemStock?> GetItemStockAsync(int itemId, int warehouseId, int unitId)
        {
            try
            {
                if (itemId <= 0)
                    throw new ArgumentException("رقم الصنف غير صحيح", nameof(itemId));
                if (warehouseId <= 0)
                    throw new ArgumentException("رقم المخزن غير صحيح", nameof(warehouseId));
                if (unitId <= 0)
                    throw new ArgumentException("رقم الوحدة غير صحيح", nameof(unitId));

                return await _itemRepository.GetItemStockAsync(itemId, warehouseId, unitId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب مخزون الصنف {ItemId}", itemId);
                throw;
            }
        }

        public async Task<bool> UpdateItemStockAsync(int itemId, int warehouseId, int unitId, decimal quantity, decimal cost)
        {
            try
            {
                if (itemId <= 0)
                    throw new ArgumentException("رقم الصنف غير صحيح", nameof(itemId));
                if (warehouseId <= 0)
                    throw new ArgumentException("رقم المخزن غير صحيح", nameof(warehouseId));
                if (unitId <= 0)
                    throw new ArgumentException("رقم الوحدة غير صحيح", nameof(unitId));

                var success = await _itemRepository.UpdateStockAsync(itemId, warehouseId, unitId, quantity, cost);
                if (success)
                {
                    _logger.LogInformation("تم تحديث مخزون الصنف {ItemId} بكمية {Quantity}", itemId, quantity);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث مخزون الصنف {ItemId}", itemId);
                throw;
            }
        }

        public async Task<string> GenerateItemCodeAsync()
        {
            try
            {
                // البحث عن آخر كود مستخدم
                var items = await _itemRepository.GetAllAsync();
                var maxCode = 0;

                foreach (var item in items)
                {
                    if (item.ItemCode.StartsWith("I") && 
                        int.TryParse(item.ItemCode.Substring(1), out var code))
                    {
                        if (code > maxCode)
                            maxCode = code;
                    }
                }

                var newCode = $"I{(maxCode + 1):D6}"; // I000001, I000002, etc.
                return newCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد كود الصنف");
                throw;
            }
        }

        public async Task<string> GenerateBarcodeAsync()
        {
            try
            {
                // توليد باركود عشوائي من 13 رقم
                var random = new Random();
                string barcode;
                
                do
                {
                    barcode = "2" + random.Next(100000000, 999999999).ToString() + random.Next(100, 999).ToString();
                } 
                while (await _itemRepository.IsBarcodeExistsAsync(barcode));

                return barcode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد الباركود");
                throw;
            }
        }

        public async Task<IEnumerable<Unit>> GetAllUnitsAsync()
        {
            try
            {
                return await _unitRepository.GetAllAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الوحدات");
                throw;
            }
        }

        public async Task<IEnumerable<ItemCategory>> GetAllCategoriesAsync()
        {
            try
            {
                return await _categoryRepository.GetAllAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب مجموعات الأصناف");
                throw;
            }
        }

        private async Task ValidateItemAsync(Item item)
        {
            if (item == null)
                throw new ArgumentNullException(nameof(item));

            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(item);

            if (!Validator.TryValidateObject(item, validationContext, validationResults, true))
            {
                var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
                throw new ValidationException($"بيانات الصنف غير صحيحة: {errors}");
            }

            // تحققات إضافية
            if (item.MinStockLevel < 0)
                throw new ValidationException("الحد الأدنى للمخزون لا يمكن أن يكون سالباً");

            if (item.MaxStockLevel < 0)
                throw new ValidationException("الحد الأقصى للمخزون لا يمكن أن يكون سالباً");

            if (item.ReorderLevel < 0)
                throw new ValidationException("نقطة إعادة الطلب لا يمكن أن تكون سالبة");

            if (item.MaxStockLevel > 0 && item.MinStockLevel > item.MaxStockLevel)
                throw new ValidationException("الحد الأدنى للمخزون لا يمكن أن يكون أكبر من الحد الأقصى");

            // التحقق من وجود الوحدة الأساسية
            if (item.BaseUnitId > 0)
            {
                var unit = await _unitRepository.GetByIdAsync(item.BaseUnitId);
                if (unit == null)
                {
                    throw new ValidationException("الوحدة الأساسية غير موجودة");
                }
            }

            // التحقق من وجود المجموعة
            if (item.CategoryId.HasValue && item.CategoryId > 0)
            {
                var category = await _categoryRepository.GetByIdAsync(item.CategoryId.Value);
                if (category == null)
                {
                    throw new ValidationException("مجموعة الصنف غير موجودة");
                }
            }
        }
    }
}
