using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace POSSystem.Models
{
    public class TransactionType : BaseEntity
    {
        [Required]
        [StringLength(20)]
        public string TypeCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string TypeName { get; set; } = string.Empty;

        [StringLength(100)]
        public string? TypeNameEn { get; set; }

        public bool IsIncome { get; set; } // true للإيرادات، false للمصروفات

        public List<FinancialTransaction> FinancialTransactions { get; set; } = new();
    }

    public class FinancialTransaction : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string TransactionNumber { get; set; } = string.Empty;

        public DateTime TransactionDate { get; set; } = DateTime.Today;

        public int TransactionTypeId { get; set; }
        public TransactionType TransactionType { get; set; } = null!;

        public int TreasuryId { get; set; }
        public Treasury Treasury { get; set; } = null!;

        public decimal Amount { get; set; }

        public int? CustomerId { get; set; }
        public Customer? Customer { get; set; }

        public int? SupplierId { get; set; }
        public Supplier? Supplier { get; set; }

        public int? SalesRepId { get; set; }
        public SalesRep? SalesRep { get; set; }

        public int? SalesInvoiceId { get; set; }
        public SalesInvoice? SalesInvoice { get; set; }

        public int? PurchaseInvoiceId { get; set; }
        public PurchaseInvoice? PurchaseInvoice { get; set; }

        [StringLength(200)]
        public string? Description { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        [StringLength(20)]
        public string Status { get; set; } = "Draft"; // Draft, Posted, Cancelled

        public int? PostedBy { get; set; }
        public DateTime? PostedDate { get; set; }
    }

    public class TreasuryTransfer : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string TransferNumber { get; set; } = string.Empty;

        public DateTime TransferDate { get; set; } = DateTime.Today;

        public int FromTreasuryId { get; set; }
        public Treasury FromTreasury { get; set; } = null!;

        public int ToTreasuryId { get; set; }
        public Treasury ToTreasury { get; set; } = null!;

        public decimal Amount { get; set; }

        [StringLength(200)]
        public string? Description { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        [StringLength(20)]
        public string Status { get; set; } = "Draft"; // Draft, Posted, Cancelled

        public int? PostedBy { get; set; }
        public DateTime? PostedDate { get; set; }
    }

    public class WarehouseTransfer : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string TransferNumber { get; set; } = string.Empty;

        public DateTime TransferDate { get; set; } = DateTime.Today;

        public int FromWarehouseId { get; set; }
        public Warehouse FromWarehouse { get; set; } = null!;

        public int ToWarehouseId { get; set; }
        public Warehouse ToWarehouse { get; set; } = null!;

        [StringLength(20)]
        public string Status { get; set; } = "Draft"; // Draft, Posted, Cancelled

        [StringLength(500)]
        public string? Notes { get; set; }

        public int? PostedBy { get; set; }
        public DateTime? PostedDate { get; set; }

        public List<WarehouseTransferDetail> WarehouseTransferDetails { get; set; } = new();
    }

    public class WarehouseTransferDetail : BaseEntity
    {
        public int TransferId { get; set; }
        public WarehouseTransfer Transfer { get; set; } = null!;

        public int ItemId { get; set; }
        public Item Item { get; set; } = null!;

        public int UnitId { get; set; }
        public Unit Unit { get; set; } = null!;

        public decimal Quantity { get; set; }
        public decimal UnitCost { get; set; } = 0;
        public decimal TotalCost { get; set; } = 0;

        public DateTime? ExpiryDate { get; set; }

        [StringLength(50)]
        public string? BatchNumber { get; set; }

        [StringLength(50)]
        public string? SerialNumber { get; set; }

        [StringLength(200)]
        public string? Notes { get; set; }
    }

    public class StockCount : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string CountNumber { get; set; } = string.Empty;

        public DateTime CountDate { get; set; } = DateTime.Today;

        public int WarehouseId { get; set; }
        public Warehouse Warehouse { get; set; } = null!;

        [StringLength(20)]
        public string Status { get; set; } = "Draft"; // Draft, InProgress, Completed, Posted, Cancelled

        [StringLength(500)]
        public string? Notes { get; set; }

        public int? CompletedBy { get; set; }
        public DateTime? CompletedDate { get; set; }

        public int? PostedBy { get; set; }
        public DateTime? PostedDate { get; set; }

        public List<StockCountDetail> StockCountDetails { get; set; } = new();
    }

    public class StockCountDetail : BaseEntity
    {
        public int StockCountId { get; set; }
        public StockCount StockCount { get; set; } = null!;

        public int ItemId { get; set; }
        public Item Item { get; set; } = null!;

        public int UnitId { get; set; }
        public Unit Unit { get; set; } = null!;

        public decimal SystemQuantity { get; set; } = 0;
        public decimal CountedQuantity { get; set; } = 0;
        public decimal DifferenceQuantity { get; set; } = 0;
        public decimal UnitCost { get; set; } = 0;
        public decimal DifferenceValue { get; set; } = 0;

        public DateTime? ExpiryDate { get; set; }

        [StringLength(50)]
        public string? BatchNumber { get; set; }

        [StringLength(50)]
        public string? SerialNumber { get; set; }

        [StringLength(200)]
        public string? Notes { get; set; }
    }

    public class StockMovement : BaseEntity
    {
        public DateTime MovementDate { get; set; } = DateTime.Now;

        [Required]
        [StringLength(50)]
        public string MovementType { get; set; } = string.Empty; // Sale, Purchase, Return, Transfer, Adjustment, Opening

        [StringLength(50)]
        public string? ReferenceType { get; set; } // SalesInvoice, PurchaseInvoice, Transfer, etc.

        public int? ReferenceId { get; set; }

        [StringLength(50)]
        public string? ReferenceNumber { get; set; }

        public int ItemId { get; set; }
        public Item Item { get; set; } = null!;

        public int WarehouseId { get; set; }
        public Warehouse Warehouse { get; set; } = null!;

        public int UnitId { get; set; }
        public Unit Unit { get; set; } = null!;

        public decimal Quantity { get; set; } // موجب للداخل، سالب للخارج
        public decimal UnitCost { get; set; } = 0;
        public decimal TotalCost { get; set; } = 0;

        public DateTime? ExpiryDate { get; set; }

        [StringLength(50)]
        public string? BatchNumber { get; set; }

        [StringLength(50)]
        public string? SerialNumber { get; set; }

        [StringLength(200)]
        public string? Notes { get; set; }
    }

    public class Account : BaseEntity
    {
        [Required]
        [StringLength(20)]
        public string AccountCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string AccountName { get; set; } = string.Empty;

        [StringLength(100)]
        public string? AccountNameEn { get; set; }

        public int? ParentAccountId { get; set; }
        public Account? ParentAccount { get; set; }

        [Required]
        [StringLength(50)]
        public string AccountType { get; set; } = string.Empty; // Asset, Liability, Equity, Revenue, Expense

        public int Level { get; set; } = 1;

        public List<Account> SubAccounts { get; set; } = new();
        public List<JournalEntryDetail> JournalEntryDetails { get; set; } = new();
    }

    public class JournalEntry : BaseEntity
    {
        [Required]
        [StringLength(50)]
        public string EntryNumber { get; set; } = string.Empty;

        public DateTime EntryDate { get; set; } = DateTime.Today;

        [StringLength(50)]
        public string? ReferenceType { get; set; }

        public int? ReferenceId { get; set; }

        [StringLength(50)]
        public string? ReferenceNumber { get; set; }

        [StringLength(200)]
        public string? Description { get; set; }

        public decimal TotalDebit { get; set; } = 0;
        public decimal TotalCredit { get; set; } = 0;

        [StringLength(20)]
        public string Status { get; set; } = "Draft"; // Draft, Posted, Cancelled

        public int? PostedBy { get; set; }
        public DateTime? PostedDate { get; set; }

        public List<JournalEntryDetail> JournalEntryDetails { get; set; } = new();
    }

    public class JournalEntryDetail : BaseEntity
    {
        public int JournalEntryId { get; set; }
        public JournalEntry JournalEntry { get; set; } = null!;

        public int AccountId { get; set; }
        public Account Account { get; set; } = null!;

        public decimal DebitAmount { get; set; } = 0;
        public decimal CreditAmount { get; set; } = 0;

        [StringLength(200)]
        public string? Description { get; set; }
    }
}
