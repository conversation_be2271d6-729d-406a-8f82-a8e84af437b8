@echo off
echo Starting POS System...
echo.

REM Check if .NET 6 is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: .NET 6 SDK is not installed or not in PATH
    echo Please install .NET 6 SDK from: https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
)

echo .NET SDK Version:
dotnet --version
echo.

REM Build the project
echo Building the project...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build successful!
echo.

REM Run the application
echo Starting the application...
dotnet run --configuration Release

pause
