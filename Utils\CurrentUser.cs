using POSSystem.Models;

namespace POSSystem
{
    public static class CurrentUser
    {
        public static User? User { get; set; }
        public static bool IsLoggedIn => User != null;
        
        public static void Login(User user)
        {
            User = user;
        }
        
        public static void Logout()
        {
            User = null;
        }
        
        public static bool HasPermission(string permissionCode)
        {
            // TODO: تطبيق فحص الصلاحيات
            return true; // مؤقتاً للاختبار
        }
    }
}
