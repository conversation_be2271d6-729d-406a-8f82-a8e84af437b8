-- نظام إدارة المبيعات والمشتريات والمخازن
-- Point of Sale and Inventory Management System Database

USE master;
GO

-- إنشاء قاعدة البيانات
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'POSSystem')
BEGIN
    CREATE DATABASE POSSystem;
END
GO

USE POSSystem;
GO

-- جدول الشركات/الفروع
CREATE TABLE Companies (
    CompanyID INT IDENTITY(1,1) PRIMARY KEY,
    CompanyName NVARCHAR(100) NOT NULL,
    Address NVARCHAR(200),
    Phone NVARCHAR(20),
    Email NVARCHAR(100),
    TaxNumber NVARCHAR(50),
    Logo VARBINARY(MAX),
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE()
);

-- جدول المستخدمين
CREATE TABLE Users (
    UserID INT IDENTITY(1,1) PRIMARY KEY,
    Username NVARCHAR(50) UNIQUE NOT NULL,
    Password NVARCHAR(255) NOT NULL,
    FullName NVARCHAR(100) NOT NULL,
    Email NVARCHAR(100),
    Phone NVARCHAR(20),
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    LastLoginDate DATETIME
);

-- جدول الصلاحيات
CREATE TABLE Permissions (
    PermissionID INT IDENTITY(1,1) PRIMARY KEY,
    PermissionName NVARCHAR(100) NOT NULL,
    PermissionCode NVARCHAR(50) UNIQUE NOT NULL,
    Description NVARCHAR(200)
);

-- جدول صلاحيات المستخدمين
CREATE TABLE UserPermissions (
    UserID INT,
    PermissionID INT,
    CanView BIT DEFAULT 0,
    CanAdd BIT DEFAULT 0,
    CanEdit BIT DEFAULT 0,
    CanDelete BIT DEFAULT 0,
    PRIMARY KEY (UserID, PermissionID),
    FOREIGN KEY (UserID) REFERENCES Users(UserID),
    FOREIGN KEY (PermissionID) REFERENCES Permissions(PermissionID)
);

-- جدول العملاء
CREATE TABLE Customers (
    CustomerID INT IDENTITY(1,1) PRIMARY KEY,
    CustomerCode NVARCHAR(20) UNIQUE NOT NULL,
    CustomerName NVARCHAR(100) NOT NULL,
    ContactPerson NVARCHAR(100),
    Address NVARCHAR(200),
    Phone NVARCHAR(20),
    Mobile NVARCHAR(20),
    Email NVARCHAR(100),
    TaxNumber NVARCHAR(50),
    CreditLimit DECIMAL(18,2) DEFAULT 0,
    PaymentTerms INT DEFAULT 0, -- عدد الأيام
    OpeningBalance DECIMAL(18,2) DEFAULT 0,
    CurrentBalance DECIMAL(18,2) DEFAULT 0,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    Notes NVARCHAR(500)
);

-- جدول الموردين
CREATE TABLE Suppliers (
    SupplierID INT IDENTITY(1,1) PRIMARY KEY,
    SupplierCode NVARCHAR(20) UNIQUE NOT NULL,
    SupplierName NVARCHAR(100) NOT NULL,
    ContactPerson NVARCHAR(100),
    Address NVARCHAR(200),
    Phone NVARCHAR(20),
    Mobile NVARCHAR(20),
    Email NVARCHAR(100),
    TaxNumber NVARCHAR(50),
    PaymentTerms INT DEFAULT 0,
    OpeningBalance DECIMAL(18,2) DEFAULT 0,
    CurrentBalance DECIMAL(18,2) DEFAULT 0,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    Notes NVARCHAR(500)
);

-- جدول المناديب
CREATE TABLE SalesReps (
    SalesRepID INT IDENTITY(1,1) PRIMARY KEY,
    SalesRepCode NVARCHAR(20) UNIQUE NOT NULL,
    SalesRepName NVARCHAR(100) NOT NULL,
    Phone NVARCHAR(20),
    Mobile NVARCHAR(20),
    Email NVARCHAR(100),
    Address NVARCHAR(200),
    CommissionRate DECIMAL(5,2) DEFAULT 0,
    OpeningBalance DECIMAL(18,2) DEFAULT 0,
    CurrentBalance DECIMAL(18,2) DEFAULT 0,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE(),
    Notes NVARCHAR(500)
);

-- جدول المخازن
CREATE TABLE Warehouses (
    WarehouseID INT IDENTITY(1,1) PRIMARY KEY,
    WarehouseCode NVARCHAR(20) UNIQUE NOT NULL,
    WarehouseName NVARCHAR(100) NOT NULL,
    Location NVARCHAR(200),
    Manager NVARCHAR(100),
    Phone NVARCHAR(20),
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE()
);

-- جدول الخزائن
CREATE TABLE Treasuries (
    TreasuryID INT IDENTITY(1,1) PRIMARY KEY,
    TreasuryCode NVARCHAR(20) UNIQUE NOT NULL,
    TreasuryName NVARCHAR(100) NOT NULL,
    OpeningBalance DECIMAL(18,2) DEFAULT 0,
    CurrentBalance DECIMAL(18,2) DEFAULT 0,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT GETDATE()
);

-- جدول مجموعات الأصناف
CREATE TABLE ItemCategories (
    CategoryID INT IDENTITY(1,1) PRIMARY KEY,
    CategoryCode NVARCHAR(20) UNIQUE NOT NULL,
    CategoryName NVARCHAR(100) NOT NULL,
    ParentCategoryID INT NULL,
    IsActive BIT DEFAULT 1,
    FOREIGN KEY (ParentCategoryID) REFERENCES ItemCategories(CategoryID)
);

-- جدول الوحدات
CREATE TABLE Units (
    UnitID INT IDENTITY(1,1) PRIMARY KEY,
    UnitCode NVARCHAR(10) UNIQUE NOT NULL,
    UnitName NVARCHAR(50) NOT NULL,
    IsActive BIT DEFAULT 1
);

-- جدول الأصناف
CREATE TABLE Items (
    ItemID INT IDENTITY(1,1) PRIMARY KEY,
    ItemCode NVARCHAR(50) UNIQUE NOT NULL,
    ItemName NVARCHAR(200) NOT NULL,
    ItemNameEn NVARCHAR(200),
    CategoryID INT,
    BaseUnitID INT,
    Barcode NVARCHAR(50),
    Description NVARCHAR(500),
    MinStockLevel DECIMAL(18,3) DEFAULT 0,
    MaxStockLevel DECIMAL(18,3) DEFAULT 0,
    ReorderLevel DECIMAL(18,3) DEFAULT 0,
    IsActive BIT DEFAULT 1,
    HasExpiry BIT DEFAULT 0,
    IsSerial BIT DEFAULT 0,
    CreatedDate DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (CategoryID) REFERENCES ItemCategories(CategoryID),
    FOREIGN KEY (BaseUnitID) REFERENCES Units(UnitID)
);

-- جدول وحدات الأصناف (متعدد الوحدات)
CREATE TABLE ItemUnits (
    ItemID INT,
    UnitID INT,
    ConversionFactor DECIMAL(18,6) NOT NULL DEFAULT 1,
    PurchasePrice DECIMAL(18,2) DEFAULT 0,
    SalePrice DECIMAL(18,2) DEFAULT 0,
    WholesalePrice DECIMAL(18,2) DEFAULT 0,
    IsDefault BIT DEFAULT 0,
    PRIMARY KEY (ItemID, UnitID),
    FOREIGN KEY (ItemID) REFERENCES Items(ItemID),
    FOREIGN KEY (UnitID) REFERENCES Units(UnitID)
);

-- جدول أرصدة الأصناف الافتتاحية
CREATE TABLE ItemOpeningBalances (
    ItemID INT,
    WarehouseID INT,
    UnitID INT,
    Quantity DECIMAL(18,3) NOT NULL,
    UnitCost DECIMAL(18,2) NOT NULL,
    TotalCost DECIMAL(18,2) NOT NULL,
    ExpiryDate DATE NULL,
    BatchNumber NVARCHAR(50) NULL,
    CreatedDate DATETIME DEFAULT GETDATE(),
    PRIMARY KEY (ItemID, WarehouseID, UnitID),
    FOREIGN KEY (ItemID) REFERENCES Items(ItemID),
    FOREIGN KEY (WarehouseID) REFERENCES Warehouses(WarehouseID),
    FOREIGN KEY (UnitID) REFERENCES Units(UnitID)
);

-- جدول أرصدة المخزون الحالية
CREATE TABLE ItemStock (
    ItemID INT,
    WarehouseID INT,
    UnitID INT,
    QuantityOnHand DECIMAL(18,3) DEFAULT 0,
    QuantityReserved DECIMAL(18,3) DEFAULT 0,
    QuantityAvailable DECIMAL(18,3) DEFAULT 0,
    AverageCost DECIMAL(18,2) DEFAULT 0,
    LastCost DECIMAL(18,2) DEFAULT 0,
    LastUpdateDate DATETIME DEFAULT GETDATE(),
    PRIMARY KEY (ItemID, WarehouseID, UnitID),
    FOREIGN KEY (ItemID) REFERENCES Items(ItemID),
    FOREIGN KEY (WarehouseID) REFERENCES Warehouses(WarehouseID),
    FOREIGN KEY (UnitID) REFERENCES Units(UnitID)
);

-- جدول عروض الأسعار
CREATE TABLE Quotations (
    QuotationID INT IDENTITY(1,1) PRIMARY KEY,
    QuotationNumber NVARCHAR(50) UNIQUE NOT NULL,
    QuotationDate DATE NOT NULL,
    CustomerID INT NOT NULL,
    SalesRepID INT,
    WarehouseID INT,
    SubTotal DECIMAL(18,2) DEFAULT 0,
    DiscountAmount DECIMAL(18,2) DEFAULT 0,
    DiscountPercent DECIMAL(5,2) DEFAULT 0,
    TaxAmount DECIMAL(18,2) DEFAULT 0,
    TaxPercent DECIMAL(5,2) DEFAULT 0,
    TotalAmount DECIMAL(18,2) DEFAULT 0,
    ValidUntil DATE,
    Status NVARCHAR(20) DEFAULT 'Draft', -- Draft, Sent, Accepted, Rejected, Expired
    Notes NVARCHAR(500),
    CreatedBy INT,
    CreatedDate DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID),
    FOREIGN KEY (SalesRepID) REFERENCES SalesReps(SalesRepID),
    FOREIGN KEY (WarehouseID) REFERENCES Warehouses(WarehouseID),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID)
);

-- جدول تفاصيل عروض الأسعار
CREATE TABLE QuotationDetails (
    QuotationDetailID INT IDENTITY(1,1) PRIMARY KEY,
    QuotationID INT NOT NULL,
    ItemID INT NOT NULL,
    UnitID INT NOT NULL,
    Quantity DECIMAL(18,3) NOT NULL,
    UnitPrice DECIMAL(18,2) NOT NULL,
    DiscountAmount DECIMAL(18,2) DEFAULT 0,
    DiscountPercent DECIMAL(5,2) DEFAULT 0,
    TaxAmount DECIMAL(18,2) DEFAULT 0,
    TaxPercent DECIMAL(5,2) DEFAULT 0,
    LineTotal DECIMAL(18,2) NOT NULL,
    Notes NVARCHAR(200),
    FOREIGN KEY (QuotationID) REFERENCES Quotations(QuotationID),
    FOREIGN KEY (ItemID) REFERENCES Items(ItemID),
    FOREIGN KEY (UnitID) REFERENCES Units(UnitID)
);

-- جدول فواتير البيع
CREATE TABLE SalesInvoices (
    SalesInvoiceID INT IDENTITY(1,1) PRIMARY KEY,
    InvoiceNumber NVARCHAR(50) UNIQUE NOT NULL,
    InvoiceDate DATE NOT NULL,
    CustomerID INT NOT NULL,
    SalesRepID INT,
    WarehouseID INT NOT NULL,
    QuotationID INT NULL,
    SubTotal DECIMAL(18,2) DEFAULT 0,
    DiscountAmount DECIMAL(18,2) DEFAULT 0,
    DiscountPercent DECIMAL(5,2) DEFAULT 0,
    TaxAmount DECIMAL(18,2) DEFAULT 0,
    TaxPercent DECIMAL(5,2) DEFAULT 0,
    TotalAmount DECIMAL(18,2) DEFAULT 0,
    PaidAmount DECIMAL(18,2) DEFAULT 0,
    RemainingAmount DECIMAL(18,2) DEFAULT 0,
    PaymentStatus NVARCHAR(20) DEFAULT 'Pending', -- Pending, Partial, Paid
    Status NVARCHAR(20) DEFAULT 'Draft', -- Draft, Posted, Cancelled
    DueDate DATE,
    Notes NVARCHAR(500),
    CreatedBy INT,
    CreatedDate DATETIME DEFAULT GETDATE(),
    PostedBy INT,
    PostedDate DATETIME,
    FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID),
    FOREIGN KEY (SalesRepID) REFERENCES SalesReps(SalesRepID),
    FOREIGN KEY (WarehouseID) REFERENCES Warehouses(WarehouseID),
    FOREIGN KEY (QuotationID) REFERENCES Quotations(QuotationID),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID),
    FOREIGN KEY (PostedBy) REFERENCES Users(UserID)
);

-- جدول تفاصيل فواتير البيع
CREATE TABLE SalesInvoiceDetails (
    SalesInvoiceDetailID INT IDENTITY(1,1) PRIMARY KEY,
    SalesInvoiceID INT NOT NULL,
    ItemID INT NOT NULL,
    UnitID INT NOT NULL,
    Quantity DECIMAL(18,3) NOT NULL,
    UnitPrice DECIMAL(18,2) NOT NULL,
    UnitCost DECIMAL(18,2) DEFAULT 0,
    DiscountAmount DECIMAL(18,2) DEFAULT 0,
    DiscountPercent DECIMAL(5,2) DEFAULT 0,
    TaxAmount DECIMAL(18,2) DEFAULT 0,
    TaxPercent DECIMAL(5,2) DEFAULT 0,
    LineTotal DECIMAL(18,2) NOT NULL,
    ExpiryDate DATE NULL,
    BatchNumber NVARCHAR(50) NULL,
    SerialNumber NVARCHAR(50) NULL,
    Notes NVARCHAR(200),
    FOREIGN KEY (SalesInvoiceID) REFERENCES SalesInvoices(SalesInvoiceID),
    FOREIGN KEY (ItemID) REFERENCES Items(ItemID),
    FOREIGN KEY (UnitID) REFERENCES Units(UnitID)
);

-- جدول مرتجعات البيع
CREATE TABLE SalesReturns (
    SalesReturnID INT IDENTITY(1,1) PRIMARY KEY,
    ReturnNumber NVARCHAR(50) UNIQUE NOT NULL,
    ReturnDate DATE NOT NULL,
    SalesInvoiceID INT NOT NULL,
    CustomerID INT NOT NULL,
    WarehouseID INT NOT NULL,
    SubTotal DECIMAL(18,2) DEFAULT 0,
    DiscountAmount DECIMAL(18,2) DEFAULT 0,
    TaxAmount DECIMAL(18,2) DEFAULT 0,
    TotalAmount DECIMAL(18,2) DEFAULT 0,
    RefundAmount DECIMAL(18,2) DEFAULT 0,
    Status NVARCHAR(20) DEFAULT 'Draft', -- Draft, Posted, Cancelled
    Reason NVARCHAR(200),
    Notes NVARCHAR(500),
    CreatedBy INT,
    CreatedDate DATETIME DEFAULT GETDATE(),
    PostedBy INT,
    PostedDate DATETIME,
    FOREIGN KEY (SalesInvoiceID) REFERENCES SalesInvoices(SalesInvoiceID),
    FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID),
    FOREIGN KEY (WarehouseID) REFERENCES Warehouses(WarehouseID),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID),
    FOREIGN KEY (PostedBy) REFERENCES Users(UserID)
);

-- جدول تفاصيل مرتجعات البيع
CREATE TABLE SalesReturnDetails (
    SalesReturnDetailID INT IDENTITY(1,1) PRIMARY KEY,
    SalesReturnID INT NOT NULL,
    SalesInvoiceDetailID INT NOT NULL,
    ItemID INT NOT NULL,
    UnitID INT NOT NULL,
    Quantity DECIMAL(18,3) NOT NULL,
    UnitPrice DECIMAL(18,2) NOT NULL,
    DiscountAmount DECIMAL(18,2) DEFAULT 0,
    TaxAmount DECIMAL(18,2) DEFAULT 0,
    LineTotal DECIMAL(18,2) NOT NULL,
    ExpiryDate DATE NULL,
    BatchNumber NVARCHAR(50) NULL,
    SerialNumber NVARCHAR(50) NULL,
    Notes NVARCHAR(200),
    FOREIGN KEY (SalesReturnID) REFERENCES SalesReturns(SalesReturnID),
    FOREIGN KEY (SalesInvoiceDetailID) REFERENCES SalesInvoiceDetails(SalesInvoiceDetailID),
    FOREIGN KEY (ItemID) REFERENCES Items(ItemID),
    FOREIGN KEY (UnitID) REFERENCES Units(UnitID)
);

-- جدول طلبيات الشراء
CREATE TABLE PurchaseOrders (
    PurchaseOrderID INT IDENTITY(1,1) PRIMARY KEY,
    OrderNumber NVARCHAR(50) UNIQUE NOT NULL,
    OrderDate DATE NOT NULL,
    SupplierID INT NOT NULL,
    WarehouseID INT NOT NULL,
    ExpectedDate DATE,
    SubTotal DECIMAL(18,2) DEFAULT 0,
    DiscountAmount DECIMAL(18,2) DEFAULT 0,
    DiscountPercent DECIMAL(5,2) DEFAULT 0,
    TaxAmount DECIMAL(18,2) DEFAULT 0,
    TaxPercent DECIMAL(5,2) DEFAULT 0,
    TotalAmount DECIMAL(18,2) DEFAULT 0,
    Status NVARCHAR(20) DEFAULT 'Draft', -- Draft, Sent, Partial, Received, Cancelled
    Notes NVARCHAR(500),
    CreatedBy INT,
    CreatedDate DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID),
    FOREIGN KEY (WarehouseID) REFERENCES Warehouses(WarehouseID),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID)
);

-- جدول تفاصيل طلبيات الشراء
CREATE TABLE PurchaseOrderDetails (
    PurchaseOrderDetailID INT IDENTITY(1,1) PRIMARY KEY,
    PurchaseOrderID INT NOT NULL,
    ItemID INT NOT NULL,
    UnitID INT NOT NULL,
    Quantity DECIMAL(18,3) NOT NULL,
    ReceivedQuantity DECIMAL(18,3) DEFAULT 0,
    UnitPrice DECIMAL(18,2) NOT NULL,
    DiscountAmount DECIMAL(18,2) DEFAULT 0,
    DiscountPercent DECIMAL(5,2) DEFAULT 0,
    TaxAmount DECIMAL(18,2) DEFAULT 0,
    TaxPercent DECIMAL(5,2) DEFAULT 0,
    LineTotal DECIMAL(18,2) NOT NULL,
    Notes NVARCHAR(200),
    FOREIGN KEY (PurchaseOrderID) REFERENCES PurchaseOrders(PurchaseOrderID),
    FOREIGN KEY (ItemID) REFERENCES Items(ItemID),
    FOREIGN KEY (UnitID) REFERENCES Units(UnitID)
);

-- جدول فواتير الشراء
CREATE TABLE PurchaseInvoices (
    PurchaseInvoiceID INT IDENTITY(1,1) PRIMARY KEY,
    InvoiceNumber NVARCHAR(50) UNIQUE NOT NULL,
    SupplierInvoiceNumber NVARCHAR(50),
    InvoiceDate DATE NOT NULL,
    SupplierID INT NOT NULL,
    WarehouseID INT NOT NULL,
    PurchaseOrderID INT NULL,
    SubTotal DECIMAL(18,2) DEFAULT 0,
    DiscountAmount DECIMAL(18,2) DEFAULT 0,
    DiscountPercent DECIMAL(5,2) DEFAULT 0,
    TaxAmount DECIMAL(18,2) DEFAULT 0,
    TaxPercent DECIMAL(5,2) DEFAULT 0,
    TotalAmount DECIMAL(18,2) DEFAULT 0,
    PaidAmount DECIMAL(18,2) DEFAULT 0,
    RemainingAmount DECIMAL(18,2) DEFAULT 0,
    PaymentStatus NVARCHAR(20) DEFAULT 'Pending', -- Pending, Partial, Paid
    Status NVARCHAR(20) DEFAULT 'Draft', -- Draft, Posted, Cancelled
    DueDate DATE,
    Notes NVARCHAR(500),
    CreatedBy INT,
    CreatedDate DATETIME DEFAULT GETDATE(),
    PostedBy INT,
    PostedDate DATETIME,
    FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID),
    FOREIGN KEY (WarehouseID) REFERENCES Warehouses(WarehouseID),
    FOREIGN KEY (PurchaseOrderID) REFERENCES PurchaseOrders(PurchaseOrderID),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID),
    FOREIGN KEY (PostedBy) REFERENCES Users(UserID)
);

-- جدول تفاصيل فواتير الشراء
CREATE TABLE PurchaseInvoiceDetails (
    PurchaseInvoiceDetailID INT IDENTITY(1,1) PRIMARY KEY,
    PurchaseInvoiceID INT NOT NULL,
    ItemID INT NOT NULL,
    UnitID INT NOT NULL,
    Quantity DECIMAL(18,3) NOT NULL,
    UnitPrice DECIMAL(18,2) NOT NULL,
    DiscountAmount DECIMAL(18,2) DEFAULT 0,
    DiscountPercent DECIMAL(5,2) DEFAULT 0,
    TaxAmount DECIMAL(18,2) DEFAULT 0,
    TaxPercent DECIMAL(5,2) DEFAULT 0,
    LineTotal DECIMAL(18,2) NOT NULL,
    ExpiryDate DATE NULL,
    BatchNumber NVARCHAR(50) NULL,
    SerialNumber NVARCHAR(50) NULL,
    Notes NVARCHAR(200),
    FOREIGN KEY (PurchaseInvoiceID) REFERENCES PurchaseInvoices(PurchaseInvoiceID),
    FOREIGN KEY (ItemID) REFERENCES Items(ItemID),
    FOREIGN KEY (UnitID) REFERENCES Units(UnitID)
);

-- جدول مرتجعات الشراء
CREATE TABLE PurchaseReturns (
    PurchaseReturnID INT IDENTITY(1,1) PRIMARY KEY,
    ReturnNumber NVARCHAR(50) UNIQUE NOT NULL,
    ReturnDate DATE NOT NULL,
    PurchaseInvoiceID INT NOT NULL,
    SupplierID INT NOT NULL,
    WarehouseID INT NOT NULL,
    SubTotal DECIMAL(18,2) DEFAULT 0,
    DiscountAmount DECIMAL(18,2) DEFAULT 0,
    TaxAmount DECIMAL(18,2) DEFAULT 0,
    TotalAmount DECIMAL(18,2) DEFAULT 0,
    RefundAmount DECIMAL(18,2) DEFAULT 0,
    Status NVARCHAR(20) DEFAULT 'Draft', -- Draft, Posted, Cancelled
    Reason NVARCHAR(200),
    Notes NVARCHAR(500),
    CreatedBy INT,
    CreatedDate DATETIME DEFAULT GETDATE(),
    PostedBy INT,
    PostedDate DATETIME,
    FOREIGN KEY (PurchaseInvoiceID) REFERENCES PurchaseInvoices(PurchaseInvoiceID),
    FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID),
    FOREIGN KEY (WarehouseID) REFERENCES Warehouses(WarehouseID),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID),
    FOREIGN KEY (PostedBy) REFERENCES Users(UserID)
);

-- جدول تفاصيل مرتجعات الشراء
CREATE TABLE PurchaseReturnDetails (
    PurchaseReturnDetailID INT IDENTITY(1,1) PRIMARY KEY,
    PurchaseReturnID INT NOT NULL,
    PurchaseInvoiceDetailID INT NOT NULL,
    ItemID INT NOT NULL,
    UnitID INT NOT NULL,
    Quantity DECIMAL(18,3) NOT NULL,
    UnitPrice DECIMAL(18,2) NOT NULL,
    DiscountAmount DECIMAL(18,2) DEFAULT 0,
    TaxAmount DECIMAL(18,2) DEFAULT 0,
    LineTotal DECIMAL(18,2) NOT NULL,
    ExpiryDate DATE NULL,
    BatchNumber NVARCHAR(50) NULL,
    SerialNumber NVARCHAR(50) NULL,
    Notes NVARCHAR(200),
    FOREIGN KEY (PurchaseReturnID) REFERENCES PurchaseReturns(PurchaseReturnID),
    FOREIGN KEY (PurchaseInvoiceDetailID) REFERENCES PurchaseInvoiceDetails(PurchaseInvoiceDetailID),
    FOREIGN KEY (ItemID) REFERENCES Items(ItemID),
    FOREIGN KEY (UnitID) REFERENCES Units(UnitID)
);

-- جدول أنواع المعاملات المالية
CREATE TABLE TransactionTypes (
    TransactionTypeID INT IDENTITY(1,1) PRIMARY KEY,
    TypeCode NVARCHAR(20) UNIQUE NOT NULL,
    TypeName NVARCHAR(100) NOT NULL,
    TypeNameEn NVARCHAR(100),
    IsIncome BIT NOT NULL, -- 1 للإيرادات، 0 للمصروفات
    IsActive BIT DEFAULT 1
);

-- جدول المعاملات المالية (سندات القبض والصرف)
CREATE TABLE FinancialTransactions (
    TransactionID INT IDENTITY(1,1) PRIMARY KEY,
    TransactionNumber NVARCHAR(50) UNIQUE NOT NULL,
    TransactionDate DATE NOT NULL,
    TransactionTypeID INT NOT NULL,
    TreasuryID INT NOT NULL,
    Amount DECIMAL(18,2) NOT NULL,
    CustomerID INT NULL,
    SupplierID INT NULL,
    SalesRepID INT NULL,
    SalesInvoiceID INT NULL,
    PurchaseInvoiceID INT NULL,
    Description NVARCHAR(200),
    Notes NVARCHAR(500),
    Status NVARCHAR(20) DEFAULT 'Draft', -- Draft, Posted, Cancelled
    CreatedBy INT,
    CreatedDate DATETIME DEFAULT GETDATE(),
    PostedBy INT,
    PostedDate DATETIME,
    FOREIGN KEY (TransactionTypeID) REFERENCES TransactionTypes(TransactionTypeID),
    FOREIGN KEY (TreasuryID) REFERENCES Treasuries(TreasuryID),
    FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID),
    FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID),
    FOREIGN KEY (SalesRepID) REFERENCES SalesReps(SalesRepID),
    FOREIGN KEY (SalesInvoiceID) REFERENCES SalesInvoices(SalesInvoiceID),
    FOREIGN KEY (PurchaseInvoiceID) REFERENCES PurchaseInvoices(PurchaseInvoiceID),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID),
    FOREIGN KEY (PostedBy) REFERENCES Users(UserID)
);

-- جدول التحويلات بين الخزائن
CREATE TABLE TreasuryTransfers (
    TransferID INT IDENTITY(1,1) PRIMARY KEY,
    TransferNumber NVARCHAR(50) UNIQUE NOT NULL,
    TransferDate DATE NOT NULL,
    FromTreasuryID INT NOT NULL,
    ToTreasuryID INT NOT NULL,
    Amount DECIMAL(18,2) NOT NULL,
    Description NVARCHAR(200),
    Notes NVARCHAR(500),
    Status NVARCHAR(20) DEFAULT 'Draft', -- Draft, Posted, Cancelled
    CreatedBy INT,
    CreatedDate DATETIME DEFAULT GETDATE(),
    PostedBy INT,
    PostedDate DATETIME,
    FOREIGN KEY (FromTreasuryID) REFERENCES Treasuries(TreasuryID),
    FOREIGN KEY (ToTreasuryID) REFERENCES Treasuries(WarehouseID),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID),
    FOREIGN KEY (PostedBy) REFERENCES Users(UserID)
);

-- جدول التحويلات بين المخازن
CREATE TABLE WarehouseTransfers (
    TransferID INT IDENTITY(1,1) PRIMARY KEY,
    TransferNumber NVARCHAR(50) UNIQUE NOT NULL,
    TransferDate DATE NOT NULL,
    FromWarehouseID INT NOT NULL,
    ToWarehouseID INT NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Draft', -- Draft, Posted, Cancelled
    Notes NVARCHAR(500),
    CreatedBy INT,
    CreatedDate DATETIME DEFAULT GETDATE(),
    PostedBy INT,
    PostedDate DATETIME,
    FOREIGN KEY (FromWarehouseID) REFERENCES Warehouses(WarehouseID),
    FOREIGN KEY (ToWarehouseID) REFERENCES Warehouses(WarehouseID),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID),
    FOREIGN KEY (PostedBy) REFERENCES Users(UserID)
);

-- جدول تفاصيل التحويلات بين المخازن
CREATE TABLE WarehouseTransferDetails (
    TransferDetailID INT IDENTITY(1,1) PRIMARY KEY,
    TransferID INT NOT NULL,
    ItemID INT NOT NULL,
    UnitID INT NOT NULL,
    Quantity DECIMAL(18,3) NOT NULL,
    UnitCost DECIMAL(18,2) DEFAULT 0,
    TotalCost DECIMAL(18,2) DEFAULT 0,
    ExpiryDate DATE NULL,
    BatchNumber NVARCHAR(50) NULL,
    SerialNumber NVARCHAR(50) NULL,
    Notes NVARCHAR(200),
    FOREIGN KEY (TransferID) REFERENCES WarehouseTransfers(TransferID),
    FOREIGN KEY (ItemID) REFERENCES Items(ItemID),
    FOREIGN KEY (UnitID) REFERENCES Units(UnitID)
);

-- جدول الجرد
CREATE TABLE StockCounts (
    StockCountID INT IDENTITY(1,1) PRIMARY KEY,
    CountNumber NVARCHAR(50) UNIQUE NOT NULL,
    CountDate DATE NOT NULL,
    WarehouseID INT NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Draft', -- Draft, InProgress, Completed, Posted, Cancelled
    Notes NVARCHAR(500),
    CreatedBy INT,
    CreatedDate DATETIME DEFAULT GETDATE(),
    CompletedBy INT,
    CompletedDate DATETIME,
    PostedBy INT,
    PostedDate DATETIME,
    FOREIGN KEY (WarehouseID) REFERENCES Warehouses(WarehouseID),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID),
    FOREIGN KEY (CompletedBy) REFERENCES Users(UserID),
    FOREIGN KEY (PostedBy) REFERENCES Users(UserID)
);

-- جدول تفاصيل الجرد
CREATE TABLE StockCountDetails (
    StockCountDetailID INT IDENTITY(1,1) PRIMARY KEY,
    StockCountID INT NOT NULL,
    ItemID INT NOT NULL,
    UnitID INT NOT NULL,
    SystemQuantity DECIMAL(18,3) DEFAULT 0,
    CountedQuantity DECIMAL(18,3) DEFAULT 0,
    DifferenceQuantity DECIMAL(18,3) DEFAULT 0,
    UnitCost DECIMAL(18,2) DEFAULT 0,
    DifferenceValue DECIMAL(18,2) DEFAULT 0,
    ExpiryDate DATE NULL,
    BatchNumber NVARCHAR(50) NULL,
    SerialNumber NVARCHAR(50) NULL,
    Notes NVARCHAR(200),
    FOREIGN KEY (StockCountID) REFERENCES StockCounts(StockCountID),
    FOREIGN KEY (ItemID) REFERENCES Items(ItemID),
    FOREIGN KEY (UnitID) REFERENCES Units(UnitID)
);

-- جدول حركات المخزون
CREATE TABLE StockMovements (
    MovementID INT IDENTITY(1,1) PRIMARY KEY,
    MovementDate DATETIME NOT NULL,
    MovementType NVARCHAR(50) NOT NULL, -- Sale, Purchase, Return, Transfer, Adjustment, Opening
    ReferenceType NVARCHAR(50), -- SalesInvoice, PurchaseInvoice, Transfer, etc.
    ReferenceID INT,
    ReferenceNumber NVARCHAR(50),
    ItemID INT NOT NULL,
    WarehouseID INT NOT NULL,
    UnitID INT NOT NULL,
    Quantity DECIMAL(18,3) NOT NULL, -- موجب للداخل، سالب للخارج
    UnitCost DECIMAL(18,2) DEFAULT 0,
    TotalCost DECIMAL(18,2) DEFAULT 0,
    ExpiryDate DATE NULL,
    BatchNumber NVARCHAR(50) NULL,
    SerialNumber NVARCHAR(50) NULL,
    Notes NVARCHAR(200),
    CreatedBy INT,
    CreatedDate DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (ItemID) REFERENCES Items(ItemID),
    FOREIGN KEY (WarehouseID) REFERENCES Warehouses(WarehouseID),
    FOREIGN KEY (UnitID) REFERENCES Units(UnitID),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID)
);

-- جدول الحسابات العامة
CREATE TABLE Accounts (
    AccountID INT IDENTITY(1,1) PRIMARY KEY,
    AccountCode NVARCHAR(20) UNIQUE NOT NULL,
    AccountName NVARCHAR(100) NOT NULL,
    AccountNameEn NVARCHAR(100),
    ParentAccountID INT NULL,
    AccountType NVARCHAR(50) NOT NULL, -- Asset, Liability, Equity, Revenue, Expense
    IsActive BIT DEFAULT 1,
    Level INT DEFAULT 1,
    FOREIGN KEY (ParentAccountID) REFERENCES Accounts(AccountID)
);

-- جدول القيود المحاسبية
CREATE TABLE JournalEntries (
    JournalEntryID INT IDENTITY(1,1) PRIMARY KEY,
    EntryNumber NVARCHAR(50) UNIQUE NOT NULL,
    EntryDate DATE NOT NULL,
    ReferenceType NVARCHAR(50),
    ReferenceID INT,
    ReferenceNumber NVARCHAR(50),
    Description NVARCHAR(200),
    TotalDebit DECIMAL(18,2) DEFAULT 0,
    TotalCredit DECIMAL(18,2) DEFAULT 0,
    Status NVARCHAR(20) DEFAULT 'Draft', -- Draft, Posted, Cancelled
    CreatedBy INT,
    CreatedDate DATETIME DEFAULT GETDATE(),
    PostedBy INT,
    PostedDate DATETIME,
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID),
    FOREIGN KEY (PostedBy) REFERENCES Users(UserID)
);

-- جدول تفاصيل القيود المحاسبية
CREATE TABLE JournalEntryDetails (
    JournalEntryDetailID INT IDENTITY(1,1) PRIMARY KEY,
    JournalEntryID INT NOT NULL,
    AccountID INT NOT NULL,
    DebitAmount DECIMAL(18,2) DEFAULT 0,
    CreditAmount DECIMAL(18,2) DEFAULT 0,
    Description NVARCHAR(200),
    FOREIGN KEY (JournalEntryID) REFERENCES JournalEntries(JournalEntryID),
    FOREIGN KEY (AccountID) REFERENCES Accounts(AccountID)
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IX_Customers_CustomerCode ON Customers(CustomerCode);
CREATE INDEX IX_Customers_CustomerName ON Customers(CustomerName);
CREATE INDEX IX_Suppliers_SupplierCode ON Suppliers(SupplierCode);
CREATE INDEX IX_Suppliers_SupplierName ON Suppliers(SupplierName);
CREATE INDEX IX_Items_ItemCode ON Items(ItemCode);
CREATE INDEX IX_Items_ItemName ON Items(ItemName);
CREATE INDEX IX_Items_Barcode ON Items(Barcode);
CREATE INDEX IX_SalesInvoices_InvoiceDate ON SalesInvoices(InvoiceDate);
CREATE INDEX IX_SalesInvoices_CustomerID ON SalesInvoices(CustomerID);
CREATE INDEX IX_PurchaseInvoices_InvoiceDate ON PurchaseInvoices(InvoiceDate);
CREATE INDEX IX_PurchaseInvoices_SupplierID ON PurchaseInvoices(SupplierID);
CREATE INDEX IX_FinancialTransactions_TransactionDate ON FinancialTransactions(TransactionDate);
CREATE INDEX IX_StockMovements_MovementDate ON StockMovements(MovementDate);
CREATE INDEX IX_StockMovements_ItemID ON StockMovements(ItemID);

-- إدراج البيانات الأساسية

-- إدراج الصلاحيات الأساسية
INSERT INTO Permissions (PermissionName, PermissionCode, Description) VALUES
(N'إدارة العملاء', 'CUSTOMERS', N'إدارة بيانات العملاء'),
(N'إدارة الموردين', 'SUPPLIERS', N'إدارة بيانات الموردين'),
(N'إدارة المناديب', 'SALESREPS', N'إدارة بيانات المناديب'),
(N'إدارة الأصناف', 'ITEMS', N'إدارة بيانات الأصناف'),
(N'إدارة المخازن', 'WAREHOUSES', N'إدارة بيانات المخازن'),
(N'إدارة الخزائن', 'TREASURIES', N'إدارة بيانات الخزائن'),
(N'فواتير البيع', 'SALES_INVOICES', N'إنشاء وإدارة فواتير البيع'),
(N'عروض الأسعار', 'QUOTATIONS', N'إنشاء وإدارة عروض الأسعار'),
(N'مرتجعات البيع', 'SALES_RETURNS', N'إنشاء وإدارة مرتجعات البيع'),
(N'فواتير الشراء', 'PURCHASE_INVOICES', N'إنشاء وإدارة فواتير الشراء'),
(N'طلبيات الشراء', 'PURCHASE_ORDERS', N'إنشاء وإدارة طلبيات الشراء'),
(N'مرتجعات الشراء', 'PURCHASE_RETURNS', N'إنشاء وإدارة مرتجعات الشراء'),
(N'المعاملات المالية', 'FINANCIAL_TRANSACTIONS', N'إدارة المعاملات المالية'),
(N'التحويلات', 'TRANSFERS', N'إدارة التحويلات بين المخازن والخزائن'),
(N'الجرد', 'STOCK_COUNT', N'إدارة عمليات الجرد'),
(N'التقارير', 'REPORTS', N'عرض التقارير'),
(N'إدارة المستخدمين', 'USERS', N'إدارة المستخدمين والصلاحيات'),
(N'الإعدادات', 'SETTINGS', N'إدارة إعدادات النظام');

-- إدراج أنواع المعاملات المالية الأساسية
INSERT INTO TransactionTypes (TypeCode, TypeName, TypeNameEn, IsIncome) VALUES
('REC_CUST', N'سند قبض من عميل', 'Receipt from Customer', 1),
('PAY_SUPP', N'سند دفع لمورد', 'Payment to Supplier', 0),
('REC_CASH', N'سند قبض نقدي', 'Cash Receipt', 1),
('PAY_CASH', N'سند صرف نقدي', 'Cash Payment', 0),
('REC_OTHER', N'سند قبض متنوع', 'Other Receipt', 1),
('PAY_OTHER', N'سند صرف متنوع', 'Other Payment', 0),
('COMM_PAY', N'دفع عمولة مندوب', 'Commission Payment', 0);

-- إدراج الوحدات الأساسية
INSERT INTO Units (UnitCode, UnitName) VALUES
('PCS', N'قطعة'),
('KG', N'كيلو جرام'),
('GM', N'جرام'),
('LTR', N'لتر'),
('MTR', N'متر'),
('BOX', N'صندوق'),
('CTN', N'كرتونة'),
('DOZ', N'دستة'),
('SET', N'طقم'),
('PKT', N'باكيت');

-- إدراج مجموعات الأصناف الأساسية
INSERT INTO ItemCategories (CategoryCode, CategoryName) VALUES
('FOOD', N'مواد غذائية'),
('ELEC', N'أجهزة كهربائية'),
('CLOTH', N'ملابس'),
('FURN', N'أثاث'),
('STAT', N'قرطاسية'),
('CLEAN', N'مواد تنظيف'),
('COSM', N'مستحضرات تجميل'),
('MED', N'أدوية'),
('TOYS', N'ألعاب'),
('MISC', N'متنوعة');

-- إدراج شركة افتراضية
INSERT INTO Companies (CompanyName, Address, Phone, Email, TaxNumber) VALUES
(N'شركة النظام التجاري', N'الرياض - المملكة العربية السعودية', '011-1234567', '<EMAIL>', '*********');

-- إدراج مستخدم افتراضي (admin)
INSERT INTO Users (Username, Password, FullName, Email, Phone) VALUES
('admin', 'admin123', N'مدير النظام', '<EMAIL>', '**********');

-- إدراج خزينة افتراضية
INSERT INTO Treasuries (TreasuryCode, TreasuryName, OpeningBalance, CurrentBalance) VALUES
('MAIN', N'الخزينة الرئيسية', 0, 0);

-- إدراج مخزن افتراضي
INSERT INTO Warehouses (WarehouseCode, WarehouseName, Location, Manager, Phone) VALUES
('MAIN', N'المخزن الرئيسي', N'الرياض', N'مدير المخزن', '011-7654321');

-- إدراج الحسابات الأساسية
INSERT INTO Accounts (AccountCode, AccountName, AccountType, Level) VALUES
('1000', N'الأصول', 'Asset', 1),
('1100', N'الأصول المتداولة', 'Asset', 2),
('1110', N'النقدية والبنوك', 'Asset', 3),
('1120', N'المخزون', 'Asset', 3),
('1130', N'العملاء', 'Asset', 3),
('2000', N'الخصوم', 'Liability', 1),
('2100', N'الخصوم المتداولة', 'Liability', 2),
('2110', N'الموردين', 'Liability', 3),
('3000', N'حقوق الملكية', 'Equity', 1),
('3100', N'رأس المال', 'Equity', 2),
('4000', N'الإيرادات', 'Revenue', 1),
('4100', N'إيرادات المبيعات', 'Revenue', 2),
('5000', N'المصروفات', 'Expense', 1),
('5100', N'تكلفة البضاعة المباعة', 'Expense', 2),
('5200', N'المصروفات التشغيلية', 'Expense', 2);

-- تحديث الحسابات الفرعية
UPDATE Accounts SET ParentAccountID = 1 WHERE AccountCode IN ('1100');
UPDATE Accounts SET ParentAccountID = 2 WHERE AccountCode IN ('1110', '1120', '1130');
UPDATE Accounts SET ParentAccountID = 3 WHERE AccountCode IN ('2100');
UPDATE Accounts SET ParentAccountID = 4 WHERE AccountCode IN ('2110');
UPDATE Accounts SET ParentAccountID = 5 WHERE AccountCode IN ('3100');
UPDATE Accounts SET ParentAccountID = 6 WHERE AccountCode IN ('4100');
UPDATE Accounts SET ParentAccountID = 7 WHERE AccountCode IN ('5100', '5200');

PRINT 'تم إنشاء قاعدة البيانات بنجاح!';
