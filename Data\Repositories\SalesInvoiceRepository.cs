using Microsoft.Extensions.Logging;
using POSSystem.Data.Interfaces;
using POSSystem.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;

namespace POSSystem.Data.Repositories
{
    public class SalesInvoiceRepository : ISalesInvoiceRepository
    {
        private readonly DatabaseManager _dbManager;
        private readonly ILogger<SalesInvoiceRepository> _logger;

        public SalesInvoiceRepository(DatabaseManager dbManager, ILogger<SalesInvoiceRepository> logger)
        {
            _dbManager = dbManager;
            _logger = logger;
        }

        public async Task<IEnumerable<SalesInvoice>> GetAllAsync()
        {
            try
            {
                var query = @"
                    SELECT si.SalesInvoiceID, si.InvoiceNumber, si.InvoiceDate, si.CustomerID,
                           si.SalesRepID, si.WarehouseID, si.QuotationID, si.SubTotal,
                           si.DiscountAmount, si.DiscountPercent, si.TaxAmount, si.TaxPercent,
                           si.TotalAmount, si.PaidAmount, si.Remaining<PERSON>mount, si.PaymentStatus,
                           si.Status, si.DueDate, si.Notes, si.CreatedBy, si.CreatedDate,
                           si.PostedBy, si.PostedDate,
                           c.CustomerName, sr.SalesRepName, w.WarehouseName
                    FROM SalesInvoices si
                    INNER JOIN Customers c ON si.CustomerID = c.CustomerID
                    LEFT JOIN SalesReps sr ON si.SalesRepID = sr.SalesRepID
                    INNER JOIN Warehouses w ON si.WarehouseID = w.WarehouseID
                    ORDER BY si.InvoiceDate DESC, si.InvoiceNumber DESC";

                var dataTable = await _dbManager.ExecuteQueryAsync(query);
                return MapDataTableToSalesInvoices(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب جميع فواتير البيع");
                throw;
            }
        }

        public async Task<SalesInvoice?> GetByIdAsync(int id)
        {
            try
            {
                var query = @"
                    SELECT si.SalesInvoiceID, si.InvoiceNumber, si.InvoiceDate, si.CustomerID,
                           si.SalesRepID, si.WarehouseID, si.QuotationID, si.SubTotal,
                           si.DiscountAmount, si.DiscountPercent, si.TaxAmount, si.TaxPercent,
                           si.TotalAmount, si.PaidAmount, si.RemainingAmount, si.PaymentStatus,
                           si.Status, si.DueDate, si.Notes, si.CreatedBy, si.CreatedDate,
                           si.PostedBy, si.PostedDate,
                           c.CustomerName, sr.SalesRepName, w.WarehouseName
                    FROM SalesInvoices si
                    INNER JOIN Customers c ON si.CustomerID = c.CustomerID
                    LEFT JOIN SalesReps sr ON si.SalesRepID = sr.SalesRepID
                    INNER JOIN Warehouses w ON si.WarehouseID = w.WarehouseID
                    WHERE si.SalesInvoiceID = @SalesInvoiceID";

                var parameters = new[] { new SqlParameter("@SalesInvoiceID", id) };
                var dataTable = await _dbManager.ExecuteQueryAsync(query, parameters);

                if (dataTable.Rows.Count == 0)
                    return null;

                return MapDataRowToSalesInvoice(dataTable.Rows[0]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب فاتورة البيع برقم {SalesInvoiceId}", id);
                throw;
            }
        }

        public async Task<SalesInvoice?> GetByNumberAsync(string invoiceNumber)
        {
            try
            {
                var query = @"
                    SELECT si.SalesInvoiceID, si.InvoiceNumber, si.InvoiceDate, si.CustomerID,
                           si.SalesRepID, si.WarehouseID, si.QuotationID, si.SubTotal,
                           si.DiscountAmount, si.DiscountPercent, si.TaxAmount, si.TaxPercent,
                           si.TotalAmount, si.PaidAmount, si.RemainingAmount, si.PaymentStatus,
                           si.Status, si.DueDate, si.Notes, si.CreatedBy, si.CreatedDate,
                           si.PostedBy, si.PostedDate,
                           c.CustomerName, sr.SalesRepName, w.WarehouseName
                    FROM SalesInvoices si
                    INNER JOIN Customers c ON si.CustomerID = c.CustomerID
                    LEFT JOIN SalesReps sr ON si.SalesRepID = sr.SalesRepID
                    INNER JOIN Warehouses w ON si.WarehouseID = w.WarehouseID
                    WHERE si.InvoiceNumber = @InvoiceNumber";

                var parameters = new[] { new SqlParameter("@InvoiceNumber", invoiceNumber) };
                var dataTable = await _dbManager.ExecuteQueryAsync(query, parameters);

                if (dataTable.Rows.Count == 0)
                    return null;

                return MapDataRowToSalesInvoice(dataTable.Rows[0]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب فاتورة البيع برقم {InvoiceNumber}", invoiceNumber);
                throw;
            }
        }

        public async Task<int> AddAsync(SalesInvoice entity)
        {
            try
            {
                var query = @"
                    INSERT INTO SalesInvoices (InvoiceNumber, InvoiceDate, CustomerID, SalesRepID,
                                             WarehouseID, QuotationID, SubTotal, DiscountAmount,
                                             DiscountPercent, TaxAmount, TaxPercent, TotalAmount,
                                             PaidAmount, RemainingAmount, PaymentStatus, Status,
                                             DueDate, Notes, CreatedBy, CreatedDate)
                    VALUES (@InvoiceNumber, @InvoiceDate, @CustomerID, @SalesRepID,
                            @WarehouseID, @QuotationID, @SubTotal, @DiscountAmount,
                            @DiscountPercent, @TaxAmount, @TaxPercent, @TotalAmount,
                            @PaidAmount, @RemainingAmount, @PaymentStatus, @Status,
                            @DueDate, @Notes, @CreatedBy, @CreatedDate);
                    SELECT SCOPE_IDENTITY();";

                var parameters = new[]
                {
                    new SqlParameter("@InvoiceNumber", entity.InvoiceNumber),
                    new SqlParameter("@InvoiceDate", entity.InvoiceDate),
                    new SqlParameter("@CustomerID", entity.CustomerId),
                    new SqlParameter("@SalesRepID", (object?)entity.SalesRepId ?? DBNull.Value),
                    new SqlParameter("@WarehouseID", entity.WarehouseId),
                    new SqlParameter("@QuotationID", (object?)entity.QuotationId ?? DBNull.Value),
                    new SqlParameter("@SubTotal", entity.SubTotal),
                    new SqlParameter("@DiscountAmount", entity.DiscountAmount),
                    new SqlParameter("@DiscountPercent", entity.DiscountPercent),
                    new SqlParameter("@TaxAmount", entity.TaxAmount),
                    new SqlParameter("@TaxPercent", entity.TaxPercent),
                    new SqlParameter("@TotalAmount", entity.TotalAmount),
                    new SqlParameter("@PaidAmount", entity.PaidAmount),
                    new SqlParameter("@RemainingAmount", entity.RemainingAmount),
                    new SqlParameter("@PaymentStatus", entity.PaymentStatus),
                    new SqlParameter("@Status", entity.Status),
                    new SqlParameter("@DueDate", (object?)entity.DueDate ?? DBNull.Value),
                    new SqlParameter("@Notes", (object?)entity.Notes ?? DBNull.Value),
                    new SqlParameter("@CreatedBy", (object?)entity.CreatedBy ?? DBNull.Value),
                    new SqlParameter("@CreatedDate", entity.CreatedDate)
                };

                var result = await _dbManager.ExecuteScalarAsync(query, parameters);
                var invoiceId = Convert.ToInt32(result);

                _logger.LogInformation("تم إضافة فاتورة البيع {InvoiceNumber} بنجاح برقم {InvoiceId}", entity.InvoiceNumber, invoiceId);
                return invoiceId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة فاتورة البيع {InvoiceNumber}", entity.InvoiceNumber);
                throw;
            }
        }

        public async Task<bool> UpdateAsync(SalesInvoice entity)
        {
            try
            {
                var query = @"
                    UPDATE SalesInvoices 
                    SET InvoiceNumber = @InvoiceNumber, InvoiceDate = @InvoiceDate,
                        CustomerID = @CustomerID, SalesRepID = @SalesRepID,
                        WarehouseID = @WarehouseID, QuotationID = @QuotationID,
                        SubTotal = @SubTotal, DiscountAmount = @DiscountAmount,
                        DiscountPercent = @DiscountPercent, TaxAmount = @TaxAmount,
                        TaxPercent = @TaxPercent, TotalAmount = @TotalAmount,
                        PaidAmount = @PaidAmount, RemainingAmount = @RemainingAmount,
                        PaymentStatus = @PaymentStatus, Status = @Status,
                        DueDate = @DueDate, Notes = @Notes
                    WHERE SalesInvoiceID = @SalesInvoiceID";

                var parameters = new[]
                {
                    new SqlParameter("@SalesInvoiceID", entity.Id),
                    new SqlParameter("@InvoiceNumber", entity.InvoiceNumber),
                    new SqlParameter("@InvoiceDate", entity.InvoiceDate),
                    new SqlParameter("@CustomerID", entity.CustomerId),
                    new SqlParameter("@SalesRepID", (object?)entity.SalesRepId ?? DBNull.Value),
                    new SqlParameter("@WarehouseID", entity.WarehouseId),
                    new SqlParameter("@QuotationID", (object?)entity.QuotationId ?? DBNull.Value),
                    new SqlParameter("@SubTotal", entity.SubTotal),
                    new SqlParameter("@DiscountAmount", entity.DiscountAmount),
                    new SqlParameter("@DiscountPercent", entity.DiscountPercent),
                    new SqlParameter("@TaxAmount", entity.TaxAmount),
                    new SqlParameter("@TaxPercent", entity.TaxPercent),
                    new SqlParameter("@TotalAmount", entity.TotalAmount),
                    new SqlParameter("@PaidAmount", entity.PaidAmount),
                    new SqlParameter("@RemainingAmount", entity.RemainingAmount),
                    new SqlParameter("@PaymentStatus", entity.PaymentStatus),
                    new SqlParameter("@Status", entity.Status),
                    new SqlParameter("@DueDate", (object?)entity.DueDate ?? DBNull.Value),
                    new SqlParameter("@Notes", (object?)entity.Notes ?? DBNull.Value)
                };

                var rowsAffected = await _dbManager.ExecuteNonQueryAsync(query, parameters);
                var success = rowsAffected > 0;

                if (success)
                    _logger.LogInformation("تم تحديث فاتورة البيع {InvoiceNumber} بنجاح", entity.InvoiceNumber);

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث فاتورة البيع {InvoiceNumber}", entity.InvoiceNumber);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            try
            {
                var query = "UPDATE SalesInvoices SET Status = 'Cancelled' WHERE SalesInvoiceID = @SalesInvoiceID";
                var parameters = new[] { new SqlParameter("@SalesInvoiceID", id) };

                var rowsAffected = await _dbManager.ExecuteNonQueryAsync(query, parameters);
                var success = rowsAffected > 0;

                if (success)
                    _logger.LogInformation("تم حذف فاتورة البيع برقم {InvoiceId} بنجاح", id);

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف فاتورة البيع برقم {InvoiceId}", id);
                throw;
            }
        }

        public async Task<bool> ExistsAsync(int id)
        {
            try
            {
                var query = "SELECT COUNT(*) FROM SalesInvoices WHERE SalesInvoiceID = @SalesInvoiceID";
                var parameters = new[] { new SqlParameter("@SalesInvoiceID", id) };

                var result = await _dbManager.ExecuteScalarAsync(query, parameters);
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من وجود فاتورة البيع برقم {InvoiceId}", id);
                throw;
            }
        }

        public async Task<bool> IsNumberExistsAsync(string invoiceNumber, int? excludeId = null)
        {
            try
            {
                var query = "SELECT COUNT(*) FROM SalesInvoices WHERE InvoiceNumber = @InvoiceNumber";
                var parameters = new List<SqlParameter> { new("@InvoiceNumber", invoiceNumber) };

                if (excludeId.HasValue)
                {
                    query += " AND SalesInvoiceID != @ExcludeId";
                    parameters.Add(new SqlParameter("@ExcludeId", excludeId.Value));
                }

                var result = await _dbManager.ExecuteScalarAsync(query, parameters.ToArray());
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من وجود رقم فاتورة البيع {InvoiceNumber}", invoiceNumber);
                throw;
            }
        }

        public async Task<string> GetNextInvoiceNumberAsync()
        {
            try
            {
                var query = @"
                    SELECT TOP 1 InvoiceNumber 
                    FROM SalesInvoices 
                    WHERE InvoiceNumber LIKE 'SI%'
                    ORDER BY SalesInvoiceID DESC";

                var result = await _dbManager.ExecuteScalarAsync(query);
                
                if (result == null)
                {
                    return "SI000001";
                }

                var lastNumber = result.ToString()!;
                if (lastNumber.StartsWith("SI") && int.TryParse(lastNumber.Substring(2), out var number))
                {
                    return $"SI{(number + 1):D6}";
                }

                return "SI000001";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد رقم فاتورة البيع التالي");
                throw;
            }
        }

        public async Task<IEnumerable<SalesInvoice>> GetInvoicesByCustomerAsync(int customerId)
        {
            try
            {
                var query = @"
                    SELECT si.SalesInvoiceID, si.InvoiceNumber, si.InvoiceDate, si.CustomerID,
                           si.SalesRepID, si.WarehouseID, si.QuotationID, si.SubTotal,
                           si.DiscountAmount, si.DiscountPercent, si.TaxAmount, si.TaxPercent,
                           si.TotalAmount, si.PaidAmount, si.RemainingAmount, si.PaymentStatus,
                           si.Status, si.DueDate, si.Notes, si.CreatedBy, si.CreatedDate,
                           si.PostedBy, si.PostedDate,
                           c.CustomerName, sr.SalesRepName, w.WarehouseName
                    FROM SalesInvoices si
                    INNER JOIN Customers c ON si.CustomerID = c.CustomerID
                    LEFT JOIN SalesReps sr ON si.SalesRepID = sr.SalesRepID
                    INNER JOIN Warehouses w ON si.WarehouseID = w.WarehouseID
                    WHERE si.CustomerID = @CustomerID
                    ORDER BY si.InvoiceDate DESC, si.InvoiceNumber DESC";

                var parameters = new[] { new SqlParameter("@CustomerID", customerId) };
                var dataTable = await _dbManager.ExecuteQueryAsync(query, parameters);
                return MapDataTableToSalesInvoices(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب فواتير البيع للعميل {CustomerId}", customerId);
                throw;
            }
        }

        public async Task<IEnumerable<SalesInvoice>> GetInvoicesByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var query = @"
                    SELECT si.SalesInvoiceID, si.InvoiceNumber, si.InvoiceDate, si.CustomerID,
                           si.SalesRepID, si.WarehouseID, si.QuotationID, si.SubTotal,
                           si.DiscountAmount, si.DiscountPercent, si.TaxAmount, si.TaxPercent,
                           si.TotalAmount, si.PaidAmount, si.RemainingAmount, si.PaymentStatus,
                           si.Status, si.DueDate, si.Notes, si.CreatedBy, si.CreatedDate,
                           si.PostedBy, si.PostedDate,
                           c.CustomerName, sr.SalesRepName, w.WarehouseName
                    FROM SalesInvoices si
                    INNER JOIN Customers c ON si.CustomerID = c.CustomerID
                    LEFT JOIN SalesReps sr ON si.SalesRepID = sr.SalesRepID
                    INNER JOIN Warehouses w ON si.WarehouseID = w.WarehouseID
                    WHERE si.InvoiceDate BETWEEN @FromDate AND @ToDate
                    ORDER BY si.InvoiceDate DESC, si.InvoiceNumber DESC";

                var parameters = new[]
                {
                    new SqlParameter("@FromDate", fromDate),
                    new SqlParameter("@ToDate", toDate)
                };

                var dataTable = await _dbManager.ExecuteQueryAsync(query, parameters);
                return MapDataTableToSalesInvoices(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب فواتير البيع للفترة من {FromDate} إلى {ToDate}", fromDate, toDate);
                throw;
            }
        }

        public async Task<IEnumerable<SalesInvoiceDetail>> GetInvoiceDetailsAsync(int invoiceId)
        {
            try
            {
                var query = @"
                    SELECT sid.SalesInvoiceDetailID, sid.SalesInvoiceID, sid.ItemID, sid.UnitID,
                           sid.Quantity, sid.UnitPrice, sid.UnitCost, sid.DiscountAmount,
                           sid.DiscountPercent, sid.TaxAmount, sid.TaxPercent, sid.LineTotal,
                           sid.ExpiryDate, sid.BatchNumber, sid.SerialNumber, sid.Notes,
                           i.ItemCode, i.ItemName, u.UnitName
                    FROM SalesInvoiceDetails sid
                    INNER JOIN Items i ON sid.ItemID = i.ItemID
                    INNER JOIN Units u ON sid.UnitID = u.UnitID
                    WHERE sid.SalesInvoiceID = @SalesInvoiceID
                    ORDER BY sid.SalesInvoiceDetailID";

                var parameters = new[] { new SqlParameter("@SalesInvoiceID", invoiceId) };
                var dataTable = await _dbManager.ExecuteQueryAsync(query, parameters);
                return MapDataTableToSalesInvoiceDetails(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب تفاصيل فاتورة البيع {InvoiceId}", invoiceId);
                throw;
            }
        }

        public async Task<bool> PostInvoiceAsync(int invoiceId, int userId)
        {
            try
            {
                var query = @"
                    UPDATE SalesInvoices 
                    SET Status = 'Posted', PostedBy = @PostedBy, PostedDate = @PostedDate
                    WHERE SalesInvoiceID = @SalesInvoiceID";

                var parameters = new[]
                {
                    new SqlParameter("@SalesInvoiceID", invoiceId),
                    new SqlParameter("@PostedBy", userId),
                    new SqlParameter("@PostedDate", DateTime.Now)
                };

                var rowsAffected = await _dbManager.ExecuteNonQueryAsync(query, parameters);
                var success = rowsAffected > 0;

                if (success)
                    _logger.LogInformation("تم ترحيل فاتورة البيع برقم {InvoiceId} بنجاح", invoiceId);

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في ترحيل فاتورة البيع برقم {InvoiceId}", invoiceId);
                throw;
            }
        }

        public async Task<IEnumerable<SalesInvoice>> SearchAsync(string searchTerm)
        {
            try
            {
                var query = @"
                    SELECT si.SalesInvoiceID, si.InvoiceNumber, si.InvoiceDate, si.CustomerID,
                           si.SalesRepID, si.WarehouseID, si.QuotationID, si.SubTotal,
                           si.DiscountAmount, si.DiscountPercent, si.TaxAmount, si.TaxPercent,
                           si.TotalAmount, si.PaidAmount, si.RemainingAmount, si.PaymentStatus,
                           si.Status, si.DueDate, si.Notes, si.CreatedBy, si.CreatedDate,
                           si.PostedBy, si.PostedDate,
                           c.CustomerName, sr.SalesRepName, w.WarehouseName
                    FROM SalesInvoices si
                    INNER JOIN Customers c ON si.CustomerID = c.CustomerID
                    LEFT JOIN SalesReps sr ON si.SalesRepID = sr.SalesRepID
                    INNER JOIN Warehouses w ON si.WarehouseID = w.WarehouseID
                    WHERE si.InvoiceNumber LIKE @SearchTerm 
                       OR c.CustomerName LIKE @SearchTerm
                    ORDER BY si.InvoiceDate DESC, si.InvoiceNumber DESC";

                var parameters = new[] { new SqlParameter("@SearchTerm", $"%{searchTerm}%") };
                var dataTable = await _dbManager.ExecuteQueryAsync(query, parameters);
                return MapDataTableToSalesInvoices(dataTable);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن فواتير البيع بالمصطلح {SearchTerm}", searchTerm);
                throw;
            }
        }

        private static IEnumerable<SalesInvoice> MapDataTableToSalesInvoices(DataTable dataTable)
        {
            var invoices = new List<SalesInvoice>();
            foreach (DataRow row in dataTable.Rows)
            {
                invoices.Add(MapDataRowToSalesInvoice(row));
            }
            return invoices;
        }

        private static SalesInvoice MapDataRowToSalesInvoice(DataRow row)
        {
            return new SalesInvoice
            {
                Id = Convert.ToInt32(row["SalesInvoiceID"]),
                InvoiceNumber = row["InvoiceNumber"].ToString()!,
                InvoiceDate = Convert.ToDateTime(row["InvoiceDate"]),
                CustomerId = Convert.ToInt32(row["CustomerID"]),
                SalesRepId = row["SalesRepID"] as int?,
                WarehouseId = Convert.ToInt32(row["WarehouseID"]),
                QuotationId = row["QuotationID"] as int?,
                SubTotal = Convert.ToDecimal(row["SubTotal"]),
                DiscountAmount = Convert.ToDecimal(row["DiscountAmount"]),
                DiscountPercent = Convert.ToDecimal(row["DiscountPercent"]),
                TaxAmount = Convert.ToDecimal(row["TaxAmount"]),
                TaxPercent = Convert.ToDecimal(row["TaxPercent"]),
                TotalAmount = Convert.ToDecimal(row["TotalAmount"]),
                PaidAmount = Convert.ToDecimal(row["PaidAmount"]),
                RemainingAmount = Convert.ToDecimal(row["RemainingAmount"]),
                PaymentStatus = row["PaymentStatus"].ToString()!,
                Status = row["Status"].ToString()!,
                DueDate = row["DueDate"] as DateTime?,
                Notes = row["Notes"] as string,
                CreatedBy = row["CreatedBy"] as int?,
                CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                PostedBy = row["PostedBy"] as int?,
                PostedDate = row["PostedDate"] as DateTime?
            };
        }

        private static IEnumerable<SalesInvoiceDetail> MapDataTableToSalesInvoiceDetails(DataTable dataTable)
        {
            var details = new List<SalesInvoiceDetail>();
            foreach (DataRow row in dataTable.Rows)
            {
                details.Add(new SalesInvoiceDetail
                {
                    Id = Convert.ToInt32(row["SalesInvoiceDetailID"]),
                    SalesInvoiceId = Convert.ToInt32(row["SalesInvoiceID"]),
                    ItemId = Convert.ToInt32(row["ItemID"]),
                    UnitId = Convert.ToInt32(row["UnitID"]),
                    Quantity = Convert.ToDecimal(row["Quantity"]),
                    UnitPrice = Convert.ToDecimal(row["UnitPrice"]),
                    UnitCost = Convert.ToDecimal(row["UnitCost"]),
                    DiscountAmount = Convert.ToDecimal(row["DiscountAmount"]),
                    DiscountPercent = Convert.ToDecimal(row["DiscountPercent"]),
                    TaxAmount = Convert.ToDecimal(row["TaxAmount"]),
                    TaxPercent = Convert.ToDecimal(row["TaxPercent"]),
                    LineTotal = Convert.ToDecimal(row["LineTotal"]),
                    ExpiryDate = row["ExpiryDate"] as DateTime?,
                    BatchNumber = row["BatchNumber"] as string,
                    SerialNumber = row["SerialNumber"] as string,
                    Notes = row["Notes"] as string
                });
            }
            return details;
        }
    }
}
