using Microsoft.Extensions.Logging;
using POSSystem.Data.Interfaces;
using POSSystem.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;

namespace POSSystem.Services
{
    public class CustomerService : ICustomerService
    {
        private readonly ICustomerRepository _customerRepository;
        private readonly ILogger<CustomerService> _logger;

        public CustomerService(ICustomerRepository customerRepository, ILogger<CustomerService> logger)
        {
            _customerRepository = customerRepository;
            _logger = logger;
        }

        public async Task<IEnumerable<Customer>> GetAllCustomersAsync()
        {
            try
            {
                return await _customerRepository.GetAllAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب جميع العملاء");
                throw;
            }
        }

        public async Task<Customer?> GetCustomerByIdAsync(int id)
        {
            try
            {
                if (id <= 0)
                    throw new ArgumentException("رقم العميل غير صحيح", nameof(id));

                return await _customerRepository.GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب العميل برقم {CustomerId}", id);
                throw;
            }
        }

        public async Task<Customer?> GetCustomerByCodeAsync(string customerCode)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(customerCode))
                    throw new ArgumentException("كود العميل مطلوب", nameof(customerCode));

                return await _customerRepository.GetByCodeAsync(customerCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب العميل بالكود {CustomerCode}", customerCode);
                throw;
            }
        }

        public async Task<int> AddCustomerAsync(Customer customer)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateCustomer(customer);

                // التحقق من عدم تكرار الكود
                if (await _customerRepository.IsCodeExistsAsync(customer.CustomerCode))
                {
                    throw new InvalidOperationException($"كود العميل '{customer.CustomerCode}' موجود مسبقاً");
                }

                // إعداد البيانات الافتراضية
                customer.CreatedDate = DateTime.Now;
                customer.IsActive = true;
                customer.CurrentBalance = customer.OpeningBalance;

                var customerId = await _customerRepository.AddAsync(customer);
                _logger.LogInformation("تم إضافة العميل {CustomerName} بنجاح برقم {CustomerId}", customer.CustomerName, customerId);

                return customerId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة العميل {CustomerName}", customer.CustomerName);
                throw;
            }
        }

        public async Task<bool> UpdateCustomerAsync(Customer customer)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateCustomer(customer);

                // التحقق من وجود العميل
                var existingCustomer = await _customerRepository.GetByIdAsync(customer.Id);
                if (existingCustomer == null)
                {
                    throw new InvalidOperationException($"العميل برقم {customer.Id} غير موجود");
                }

                // التحقق من عدم تكرار الكود
                if (await _customerRepository.IsCodeExistsAsync(customer.CustomerCode, customer.Id))
                {
                    throw new InvalidOperationException($"كود العميل '{customer.CustomerCode}' موجود مسبقاً");
                }

                // الحفاظ على البيانات الأساسية
                customer.CreatedDate = existingCustomer.CreatedDate;
                customer.CreatedBy = existingCustomer.CreatedBy;
                customer.ModifiedDate = DateTime.Now;

                var success = await _customerRepository.UpdateAsync(customer);
                if (success)
                {
                    _logger.LogInformation("تم تحديث العميل {CustomerName} بنجاح", customer.CustomerName);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث العميل {CustomerName}", customer.CustomerName);
                throw;
            }
        }

        public async Task<bool> DeleteCustomerAsync(int id)
        {
            try
            {
                if (id <= 0)
                    throw new ArgumentException("رقم العميل غير صحيح", nameof(id));

                // التحقق من وجود العميل
                var customer = await _customerRepository.GetByIdAsync(id);
                if (customer == null)
                {
                    throw new InvalidOperationException($"العميل برقم {id} غير موجود");
                }

                // TODO: التحقق من عدم وجود معاملات مرتبطة بالعميل

                var success = await _customerRepository.DeleteAsync(id);
                if (success)
                {
                    _logger.LogInformation("تم حذف العميل {CustomerName} بنجاح", customer.CustomerName);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف العميل برقم {CustomerId}", id);
                throw;
            }
        }

        public async Task<bool> IsCustomerCodeExistsAsync(string customerCode, int? excludeId = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(customerCode))
                    return false;

                return await _customerRepository.IsCodeExistsAsync(customerCode, excludeId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من وجود كود العميل {CustomerCode}", customerCode);
                throw;
            }
        }

        public async Task<IEnumerable<Customer>> SearchCustomersAsync(string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return await GetAllCustomersAsync();

                return await _customerRepository.SearchAsync(searchTerm);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في البحث عن العملاء بالمصطلح {SearchTerm}", searchTerm);
                throw;
            }
        }

        public async Task<IEnumerable<Customer>> GetActiveCustomersAsync()
        {
            try
            {
                return await _customerRepository.GetActiveCustomersAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب العملاء النشطين");
                throw;
            }
        }

        public async Task<decimal> GetCustomerBalanceAsync(int customerId)
        {
            try
            {
                if (customerId <= 0)
                    throw new ArgumentException("رقم العميل غير صحيح", nameof(customerId));

                return await _customerRepository.GetCustomerBalanceAsync(customerId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب رصيد العميل برقم {CustomerId}", customerId);
                throw;
            }
        }

        public async Task<bool> UpdateCustomerBalanceAsync(int customerId, decimal amount)
        {
            try
            {
                if (customerId <= 0)
                    throw new ArgumentException("رقم العميل غير صحيح", nameof(customerId));

                var success = await _customerRepository.UpdateBalanceAsync(customerId, amount);
                if (success)
                {
                    _logger.LogInformation("تم تحديث رصيد العميل برقم {CustomerId} بمبلغ {Amount}", customerId, amount);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث رصيد العميل برقم {CustomerId}", customerId);
                throw;
            }
        }

        public async Task<string> GenerateCustomerCodeAsync()
        {
            try
            {
                // البحث عن آخر كود مستخدم
                var customers = await _customerRepository.GetAllAsync();
                var maxCode = 0;

                foreach (var customer in customers)
                {
                    if (customer.CustomerCode.StartsWith("C") && 
                        int.TryParse(customer.CustomerCode.Substring(1), out var code))
                    {
                        if (code > maxCode)
                            maxCode = code;
                    }
                }

                var newCode = $"C{(maxCode + 1):D6}"; // C000001, C000002, etc.
                return newCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في توليد كود العميل");
                throw;
            }
        }

        private static void ValidateCustomer(Customer customer)
        {
            if (customer == null)
                throw new ArgumentNullException(nameof(customer));

            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(customer);

            if (!Validator.TryValidateObject(customer, validationContext, validationResults, true))
            {
                var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
                throw new ValidationException($"بيانات العميل غير صحيحة: {errors}");
            }

            // تحققات إضافية
            if (customer.CreditLimit < 0)
                throw new ValidationException("حد الائتمان لا يمكن أن يكون سالباً");

            if (customer.PaymentTerms < 0)
                throw new ValidationException("شروط الدفع لا يمكن أن تكون سالبة");

            if (!string.IsNullOrEmpty(customer.Email) && !IsValidEmail(customer.Email))
                throw new ValidationException("البريد الإلكتروني غير صحيح");
        }

        private static bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
}
